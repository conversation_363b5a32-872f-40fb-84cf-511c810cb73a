<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="hr_fleet_rule_vehicle_visibility_hr_officier" model="ir.rule">
            <field name="name">Hr Officer read rights on vehicle with employees assigned</field>
            <field name="model_id" ref="model_fleet_vehicle"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
            <field name="groups" eval="[(4, ref('hr.group_hr_user'))]"/>
            <field name="domain_force">['|', ('driver_employee_id', '!=', False), ('future_driver_employee_id', '!=', False)]</field>
        </record>

        <record id="fleet_rule_contract_visibility_user" model="ir.rule">
            <field name="name">User can see contracts from his/her corresponding employee</field>
            <field name="model_id" ref="model_fleet_vehicle_log_contract"/>
            <field name="groups" eval="[(4, ref('fleet.fleet_group_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
            <field name="domain_force">[('vehicle_id.driver_employee_id.user_id','=',user.id)]</field>
        </record>
        <record id="fleet_rule_service_visibility_user" model="ir.rule">
            <field name="name">User can see vehicle's services from his/her corresponding employee</field>
            <field name="model_id" ref="model_fleet_vehicle_log_services"/>
            <field name="groups" eval="[(4, ref('fleet.fleet_group_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
            <field name="domain_force">[('vehicle_id.driver_employee_id.user_id','=',user.id)]</field>
        </record>
        <record id="fleet_rule_odometer_visibility_user" model="ir.rule">
            <field name="name">User can see vehicle's odometer from his/her corresponding employee</field>
            <field name="model_id" ref="model_fleet_vehicle_odometer"/>
            <field name="groups" eval="[(4, ref('fleet.fleet_group_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
            <field name="domain_force">[('vehicle_id.driver_employee_id.user_id','=',user.id)]</field>
        </record>
        <record id="fleet_rule_vehicle_visibility_user" model="ir.rule">
            <field name="name">User can see vehicles from his/her corresponding employee</field>
            <field name="model_id" ref="model_fleet_vehicle"/>
            <field name="groups" eval="[(4, ref('fleet.fleet_group_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
            <field name="domain_force">[('driver_employee_id.user_id','=',user.id)]</field>
        </record>
    </data>
</odoo>
