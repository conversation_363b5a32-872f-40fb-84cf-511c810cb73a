# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:48+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_link_wizard.py:0
msgid ""
"#%(number)s - Installment of <strong>%(amount)s</strong> due on <strong "
"class=\"text-primary\">%(date)s</strong>"
msgstr ""
"#%(number)s - قسط لـ <strong>%(amount)s</strong> مستحق بتاريخ <strong "
"class=\"text-primary\">%(date)s</strong> "

#. module: account_payment
#. odoo-javascript
#: code:addons/account_payment/static/src/js/portal_my_invoices_payment.js:0
msgid "%s day(s) overdue"
msgstr "متأخر لمدة %s أيام "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid "<b>Communication: </b>"
msgstr "<b>التواصل: </b>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"Pay Now</span>"
msgstr ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"ادفع الآن</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-arrow-circle-right\"/> Pay Now"
msgstr "<i class=\"fa fa-fw fa-arrow-circle-right\"/> ادفع الآن "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Authorized</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> مصرح له</span> "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Paid</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> تم الدفع</span> "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> تم الدفع "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Pending"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> قيد الانتظار"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Processing Payment"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> جاري معالجة المدفوعات "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "<span class=\"d-none d-md-inline\"> Pending</span>"
msgstr "<span class=\"d-none d-md-inline\"> قيد الانتظار</span> "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "<strong>Full Amount</strong><br/>"
msgstr "<strong>كامل المبلغ</strong><br/> "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid ""
"<strong>Installment</strong>\n"
"                                        <br/>"
msgstr ""
"<strong>أقساط</strong>\n"
"                                        <br/> "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment provider configuration."
msgstr ""
"<strong>تحذير!</strong> توجد عملية استرداد أموال معلقة لهذا الدفع.\n"
"                        انتظر قليلاً ريثما يتم معالجتها. إذا ما زالت عملية استرداد الأموال\n"
"                        معلقة بعد عدة دقائق، يرجى التحقق من تهيئة مزود الدفع. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_link_wizard.py:0
msgid "A discount will be applied if the customer pays before %s included."
msgstr "سيتم تطبيق خصم إذا قام العميل بالدفع قبل أن يتم تضمين %s. "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid ""
"A payment has already been made on this invoice, please make sure to not pay"
" twice."
msgstr ""
"لقد تم سداد قيمة هذه الفاتورة بالفعل، يُرجى التأكد من عدم الدفع مرتين. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
msgid "A payment transaction with reference %s already exists."
msgstr "توجد معاملة دفع لها هذا المرجع %s بالفعل. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
msgid "A token is required to create a new payment transaction."
msgstr "تحتاج إلى رمز لإنشاء معاملة دفع جديدة. "

#. module: account_payment
#: model:onboarding.onboarding.step,button_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Activate Stripe"
msgstr "تفعيل Stripe "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_overdue_invoices_page
msgid "Amount"
msgstr "مبلغ"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__amount_available_for_refund
msgid "Amount Available For Refund"
msgstr "المبلغ المتاح لاسترداد الأموال "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__invoice_amount_due
msgid "Amount Due"
msgstr "المبلغ المستحق"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__amount_paid
#: model:ir.model.fields,field_description:account_payment.field_account_move__amount_paid
msgid "Amount paid"
msgstr "المبلغ المدفوع"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"هل أنت متأكد أنك تريد إبطال المعاملة المُصرح بها؟ لا يمكن التراجع عن هذا "
"الإجراء. "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "المعاملات المُصرح بها"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Capture Transaction"
msgstr "تسجيل المعاملة "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment_paid
msgid "Close"
msgstr "إغلاق"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_method_code
msgid "Code"
msgstr "رمز "

#. module: account_payment
#: model:ir.model,name:account_payment.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__currency_id
msgid "Currency"
msgstr "العملة"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__discount_date
msgid "Discount Date"
msgstr "تاريخ الخصم"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__display_open_installments
msgid "Display Open Installments"
msgstr "عرض الأقساط المفتوحة "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid ""
"Done, your online payment has been successfully processed. Thank you for "
"your order."
msgstr "لقد انتهيت، تمت معالجة عملية الدفع عبر الإنترنت بنجاح. شكراً لطلبك. "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__epd_info
msgid "Early Payment Discount Information"
msgstr "معلومات خصم الدفع المبكر "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Early Payment Discount of"
msgstr "خصم الدفع المبكر بقيمة "

#. module: account_payment
#: model:onboarding.onboarding.step,description:account_payment.onboarding_onboarding_step_payment_provider
msgid "Enable credit & debit card payments supported by Stripe."
msgstr ""
"تمكين الدفع عن طريق البطاقات الائتمانية وبطاقات الخصم المدعومة من قِبَل "
"Stripe "

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__full_only
msgid "Full Only"
msgstr "كامل فقط "

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "إنشاء رابط دفع المبيعات "

#. module: account_payment
#: model:ir.actions.act_window,name:account_payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "إنشاء رابط دفع "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__has_eligible_epd
msgid "Has Eligible Epd"
msgstr "يحتوي على Epd مؤهل "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__has_pending_refund
msgid "Has a pending refund"
msgstr "يحتوي على عملية استرداد أموال معلقة "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__id
msgid "ID"
msgstr "المُعرف"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid ""
"Impossible to pay all the overdue invoices if they don't share the same "
"currency."
msgstr "لا يمكن دفع كافة الفواتير المتأخرة إذا لم تكن لها نفس العملة. "

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_method_line__payment_provider_state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""
"في وضع الاختبار، تتم معالجة دفع مزيف عن طريق واجهة دفع تجريبية. \n"
"يُنصح بهذه الوضعية عند ضبط مزود الدفع. "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.res_config_settings_view_form
msgid "Invoice Online Payment"
msgstr "سداد قيمة الفاتورة عبر الإنترنت "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_transaction_form
msgid "Invoice(s)"
msgstr "فاتورة (فواتير) "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoice_ids
msgid "Invoices"
msgstr "فواتير العملاء "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_home_overdue_invoice
msgid "Invoices &amp; Bills"
msgstr "فواتير العملاء والموردين "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoices_count
msgid "Invoices Count"
msgstr "عدد الفواتير "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_home_account_payment
msgid "Invoices to pay"
msgstr "الفواتير المراد دفعها "

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_journal
msgid "Journal"
msgstr "دفتر اليومية"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_available_for_refund
msgid "Maximum Refund Allowed"
msgstr "الحد الأقصى المسموح به لاسترداد الأموال "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_link_wizard__form_inherit_account_payment
msgid "Next Installments"
msgstr "الأقساط القادمة "

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__payment_token_id
msgid ""
"Note that only tokens from providers allowing to capture the amount are "
"available."
msgstr ""
"يرجى الملاحظة بأن الرموز التي تأتي من مزودي الدفع التي تسمح بتحصيل المبلغ "
"فقط هي المتاحة. "

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_register__payment_token_id
msgid ""
"Note that tokens from providers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""
"يرجى الملاحظة بأن الرموز التي تأتي من مزودي الدفع التي تم ضبطها لتفويض "
"المعاملات فقط (عوضاً عن تحصيل المبلغ) غير متاحة. "

#. module: account_payment
#: model:onboarding.onboarding.step,step_image_alt:account_payment.onboarding_onboarding_step_payment_provider
msgid "Onboarding Online Payments"
msgstr "تهيئة الدفع عبر الإنترنت "

#. module: account_payment
#: model:ir.model,name:account_payment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "خطة تمهيدية "

#. module: account_payment
#: model:onboarding.onboarding.step,title:account_payment.onboarding_onboarding_step_payment_provider
msgid "Online Payments"
msgstr "المدفوعات عبر الإنترنت "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_link_wizard.py:0
msgid "Online payment option is not enabled in Configuration."
msgstr "لم يتم تمكين خيار الدفع عبر الإنترنت في التهيئة. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/portal.py:0
msgid "Overdue invoices should share the same company."
msgstr "يجب أن تكون للفواتير المتأخرة نفس الشركة. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/portal.py:0
msgid "Overdue invoices should share the same currency."
msgstr "يجب أن تكون للفواتير المتأخرة نفس العملة. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/portal.py:0
msgid "Overdue invoices should share the same partner."
msgstr "يجب أن تكون للفواتير المتأخرة نفس الشريك. "

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__partial
msgid "Partial"
msgstr "جزئي"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Pay"
msgstr "الدفع "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment_paid
msgid "Pay Invoice"
msgstr "دفع مبلغ الفاتورة "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_res_config_settings__pay_invoices_online
msgid "Pay Invoices Online"
msgstr "ادفع الفواتير عبر الإنترنت "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_docs_entry
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay Now"
msgstr "ادفع الآن "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay now"
msgstr "ادفع الآن "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_home_overdue_invoice
msgid "Pay overdue"
msgstr "دفع المبلغ المتأخر "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_id
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__payment_id
msgid "Payment"
msgstr "الدفع "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_amount
msgid "Payment Amount"
msgstr "المبلغ المدفوع"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_provider__journal_id
msgid "Payment Journal"
msgstr "دفتر يومية الدفع "

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_method
#: model:ir.model,name:account_payment.model_account_payment_method_line
#: model:ir.ui.menu,name:account_payment.payment_method_menu
msgid "Payment Methods"
msgstr "طرق الدفع "

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_provider
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_id
msgid "Payment Provider"
msgstr "مزود الدفع "

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_provider_menu
msgid "Payment Providers"
msgstr "مزودي الدفع "

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_refund_wizard
msgid "Payment Refund Wizard"
msgstr "معالج استرداد المدفوعات "

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_token_menu
msgid "Payment Tokens"
msgstr "رموز الدفع "

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_transaction
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_transaction_id
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__transaction_id
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Payment Transaction"
msgstr "معاملة الدفع "

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_transaction_menu
msgid "Payment Transactions"
msgstr "معاملات الدفع "

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment
msgid "Payments"
msgstr "الدفعات"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid "Please log in to pay your overdue invoices"
msgstr "يرجى تسجيل الدخول لدفع فواتيرك المتأخرة "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
msgid "Provider"
msgstr "المزود"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_overdue_invoices_page
msgid "Reference"
msgstr "الرقم المرجعي "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__support_refund
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
msgid "Refund"
msgstr "استرداد الأموال "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_to_refund
msgid "Refund Amount"
msgstr "المبلغ المراد استرداده "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__refunded_amount
msgid "Refunded Amount"
msgstr "المبلغ المسترد "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
msgid "Refunds"
msgstr "الاستردادات "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__refunds_count
msgid "Refunds Count"
msgstr "عدد عمليات استرداد الأموال "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_journal_form
msgid "SETUP"
msgstr "الضبط "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_token_id
msgid "Saved Payment Token"
msgstr " رمز الدفع المحفزظ "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_token_id
msgid "Saved payment token"
msgstr " رمز الدفع المحفزظ "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__source_payment_id
msgid "Source Payment"
msgstr "الدفع المصدري "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_state
msgid "State"
msgstr "الولاية "

#. module: account_payment
#: model:onboarding.onboarding.step,done_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Step Completed!"
msgstr "تم اكتمال الخطوة!"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__suitable_payment_token_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__suitable_payment_token_ids
msgid "Suitable Payment Token"
msgstr "رمز الدفع المناسب "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid "The access token is invalid."
msgstr "رمز الوصول غير صالح. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_refund_wizard.py:0
msgid ""
"The amount to be refunded must be positive and cannot be superior to %s."
msgstr "يجب أن يكون المبلغ المراد استرداده موجباً ولا يمكن أن يتخطى %s. "

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_payment_provider__journal_id
msgid "The journal in which the successful transactions are posted."
msgstr "دفتر اليومية الذي يتم ترحيل المعاملات الناجحة فيه. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
msgid ""
"The payment related to the transaction with reference %(ref)s has been "
"posted: %(link)s"
msgstr "لقد تم ترحيل الدفع المتعلق بالمعاملة ذات المرجع %(ref)s: %(link)s "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid "The provided parameters are invalid."
msgstr "المعايير المقدمة غير صالحة. "

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__source_payment_id
msgid "The source payment of related refund payments"
msgstr "الدفع المصدري للمدفوعات المستردة ذات الصلة "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "There are pending transactions for this invoice."
msgstr "توجد معاملات معلقة لهذه الفاتورة. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "There is no amount to be paid."
msgstr "لا يوجد مبلغ يجب دفعه. "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: invalid invoice."
msgstr "حدث خطأ أثناء معالجة عملية الدفع: الفاتورة غير صالحة. "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid ""
"There was an error processing your payment: issue with credit card ID "
"validation."
msgstr ""
"حدث خطأ أثناء معالجة عملية الدفع: هناك مشكلة في تصديق معرف البطاقة "
"الائتمانية. "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: transaction failed.<br/>"
msgstr "حدث خطأ أثناء معالجة عملية الدفع: فشلت المعاملة.<br/>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was en error processing your payment: invalid credit card ID."
msgstr "حدث خطأ أثناء معالجة عملية الدفع: معرف البطاقة الائتمانية غير صالح. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This invoice cannot be paid online."
msgstr "لا يمكن سداد هذه الفاتورة عبر الإنترنت. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This invoice has already been paid."
msgstr "لقد تم دفع قيمة هذه الفاتورة بالفعل. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This invoice isn't posted."
msgstr "لم يتم ترحيل هذه الفاتورة. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This is not an outgoing invoice."
msgstr "هذه ليست فاتورة جارية. "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__transaction_count
#: model:ir.model.fields,field_description:account_payment.field_account_move__transaction_count
msgid "Transaction Count"
msgstr "عدد المعاملات "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__transaction_ids
msgid "Transactions"
msgstr "المعاملات "

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__none
msgid "Unsupported"
msgstr "غير مدعوم "

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__use_electronic_payment_method
msgid "Use Electronic Payment Method"
msgstr "استخدام طريقة دفع إلكترونية "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Void Transaction"
msgstr "إبطال المعاملة"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
msgid ""
"You can't delete a payment method that is linked to a provider in the enabled or test state.\n"
"Linked providers(s): %s"
msgstr ""
"لا يمكنك حذف طريقة دفع مرتبطة بمزود دفع في وضع التمكين أو الاختبار. \n"
"مزودو الدفع المرتبطون: %s "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_provider.py:0
msgid ""
"You cannot uninstall this module as payments using this payment method "
"already exist."
msgstr ""
"لا يمكنك إلغاء تثبيت هذا التطبيق لأنه هناك عمليات دفع تستخدم طريقة الدفع هذه"
" بالفعل. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_journal.py:0
msgid ""
"You must first deactivate a payment provider before deleting its journal.\n"
"Linked providers: %s"
msgstr ""
"عليك أولاً إلغاء تفعيل مزود الدفع قبل حذف دفتر اليومية الخاص به. \n"
"مزودو الدفع المرتبطون: %s "

#. module: account_payment
#. odoo-javascript
#: code:addons/account_payment/static/src/js/portal_my_invoices_payment.js:0
msgid "due in %s day(s)"
msgstr "مستحقة في %s أيام "

#. module: account_payment
#. odoo-javascript
#: code:addons/account_payment/static/src/js/portal_my_invoices_payment.js:0
msgid "due today"
msgstr "مستحق اليوم"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "has been applied."
msgstr "تم تطبيقها. "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "overdue"
msgstr "متأخر"
