<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.hr.contract</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="hr.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//block[@name='employee_rights_setting_container']" position="after">
                <block title="Contract" id="hr_contract">
                    <setting string="Contract Expiration Notice Period" id="contract_notice_period"
                        title="Number of days prior to the contract end date that a contract expiration warning is triggered.">
                        <field name="contract_expiration_notice_period" class="w-25"/><span>Days</span>
                    </setting>
                    <setting string="Work Permit Expiration Notice Period" id="work_permit_notice_period"
                        title="Number of days prior to the work permit expiration date that a warning is triggered.">
                        <field name="work_permit_expiration_notice_period" class="w-25"/><span>Days</span>
                    </setting>
                </block>
            </xpath>
        </field>
    </record>

</odoo>
