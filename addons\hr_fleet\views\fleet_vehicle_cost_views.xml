<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="fleet_vehicle_log_contract_view_form_inherit_hr" model="ir.ui.view">
        <field name="name">fleet.vehicle.log.contract.form.inherit.hr</field>
        <field name="model">fleet.vehicle.log.contract</field>
        <field name="inherit_id" ref="fleet.fleet_vehicle_log_contract_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='purchaser_id']" position="after">
                <field name="purchaser_employee_id" string="Driver" invisible="1"/>
            </xpath>
            <sheet position="inside">
                <div class="oe_button_box" name="button_box">
                    <button name="action_open_employee" type="object" class="oe_stat_button" icon="fa-id-card-o" groups="hr.group_hr_user" invisible="not purchaser_employee_id">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_text">Employee</span>
                            <span class="o_stat_value"><field name="purchaser_employee_id"/></span>
                        </div>
                    </button>
                </div>
            </sheet>
        </field>
    </record>

    <record id="fleet_vehicle_log_contract_view_tree_inherit_hr" model="ir.ui.view">
        <field name="name">fleet.vehicle.log.contract.list.inherit.hr</field>
        <field name="model">fleet.vehicle.log.contract</field>
        <field name="inherit_id" ref="fleet.fleet_vehicle_log_contract_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='purchaser_id']" position="after">
                <field name="purchaser_employee_id" optional="hide" widget="many2one_avatar_user"/>
            </xpath>
        </field>
    </record>

    <record id="fleet_vehicle_log_contract_view_search_inherit_hr" model="ir.ui.view">
        <field name="name">fleet.vehicle.log.contract.search.inherit.hr</field>
        <field name="model">fleet.vehicle.log.contract</field>
        <field name="inherit_id" ref="fleet.fleet_vehicle_log_contract_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='purchaser_id']" position="after">
                <field name="purchaser_employee_id" string="Employee"
                    filter_domain="[('purchaser_employee_id','child_of', self)]"/>
            </xpath>
        </field>
    </record>

    <record id="fleet_vehicle_log_services_view_form_inherit_hr" model="ir.ui.view">
        <field name="name">fleet.vehicle.log.contract.form.inherit.hr</field>
        <field name="model">fleet.vehicle.log.services</field>
        <field name="inherit_id" ref="fleet.fleet_vehicle_log_services_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='purchaser_id']" position="after">
                <field name="purchaser_employee_id" string="Driver (Employee)" invisible="1"/>
            </xpath>
        </field>
    </record>

    <record id="fleet_vehicle_log_services_view_tree_inherit_hr" model="ir.ui.view">
        <field name="name">fleet.vehicle.log.services.list.inherit.hr</field>
        <field name="model">fleet.vehicle.log.services</field>
        <field name="inherit_id" ref="fleet.fleet_vehicle_log_services_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='purchaser_id']" position="after">
                <field name="purchaser_employee_id" readonly="1" widget="many2one_avatar_user" optional="hide"/>
            </xpath>
        </field>
    </record>

    <record id="fleet_vehicle_log_services_view_kanban_inherit_hr" model="ir.ui.view">
        <field name="name">fleet.vehicle.log.services.kanban.inherit.hr</field>
        <field name="model">fleet.vehicle.log.services</field>
        <field name="inherit_id" ref="fleet.fleet_vehicle_log_services_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//div/field[@name='purchaser_id']" position="after">
                <field name="purchaser_employee_id"/>
            </xpath>
        </field>
    </record>
</odoo>
