# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_qr_code_emv
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid "A bank account is required for EMV QR Code generation."
msgstr "Cần có tài khoản ngân hàng để tạo Mã QR EMV."

#. module: account_qr_code_emv
#: model:ir.model,name:account_qr_code_emv.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Tài khoản Ngân hàng"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__display_qr_setting
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__display_qr_setting
msgid "Display Qr Setting"
msgstr "Hiện thị cài đặt QR"

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid "EMV Merchant-Presented QR-code"
msgstr "Mã QR của người bán EMV"

#. module: account_qr_code_emv
#: model_terms:ir.ui.view,arch_db:account_qr_code_emv.view_partner_bank_form_inherit_account
msgid "EMV QR Configuration"
msgstr "Cấu hình QR EMV"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__include_reference
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__include_reference
msgid "Include Reference"
msgstr "Bao gồm tham chiếu"

#. module: account_qr_code_emv
#: model:ir.model.fields,help:account_qr_code_emv.field_account_setup_bank_manual_config__include_reference
#: model:ir.model.fields,help:account_qr_code_emv.field_res_partner_bank__include_reference
msgid "Include the reference in the QR code."
msgstr "Bao gồm tham chiếu trong mã QR."

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid "Missing Merchant Account Information."
msgstr "Thiếu thông tin tài khoản người bán"

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid "Missing Merchant City."
msgstr "Thiếu thành phố của người bán."

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid "Missing Proxy Type."
msgstr "Thiếu loại proxy."

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid "Missing Proxy Value."
msgstr "Thiếu giá trị proxy."

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
msgid ""
"No EMV QR Code is available for the country of the account "
"%(account_number)s."
msgstr ""
"Không có Mã QR EMV nào có sẵn cho quốc gia của tài khoản %(account_number)s."

#. module: account_qr_code_emv
#: model:ir.model.fields.selection,name:account_qr_code_emv.selection__res_partner_bank__proxy_type__none
msgid "None"
msgstr "Không"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__proxy_type
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__proxy_type
msgid "Proxy Type"
msgstr "Loại proxy"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__proxy_value
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__proxy_value
msgid "Proxy Value"
msgstr "Giá trị proxy"
