<?xml version="1.0"?>
<odoo><data noupdate="1">

    <record id="mail_template_demo_crm_lead" model="mail.template">
        <field name="name">Welcome Demo</field>
        <field name="model_id" ref="crm.model_crm_lead"/>
        <field name="partner_to">{{ object.partner_id != False and object.partner_id.id }}</field>
        <field name="email_to">{{ (not object.partner_id and object.email_from) }}</field>
        <field name="body_html" type="html">
<table border="0" cellpadding="0" cellspacing="0" style="padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;"><tr><td align="center">
<table border="0" cellpadding="0" cellspacing="0" width="590" style="padding: 24px; background-color: white; color: #454748; border-collapse:separate;">
<tbody>
    <!-- HEADER -->
    <tr>
        <td align="center" style="min-width: 590px;">
            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: white; padding: 0; border-collapse:separate;">
                <tr><td valign="middle">
                    <span style="font-size: 10px;">Your Lead/Opportunity</span><br/>
                    <span style="font-size: 20px; font-weight: bold;" t-out="object.name or ''">Interest in your products</span>
                </td><td valign="middle" align="right">
                    <img t-attf-src="/logo.png?company={{ object.company_id.id }}" style="padding: 0px; margin: 0px; height: 48px;" t-att-alt="object.company_id.name"/>
                </td></tr>
                <tr><td colspan="2" style="text-align:center;">
                    <hr width="100%" style="background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:4px 0px 32px 0px;"/>
                </td></tr>
            </table>
        </td>
    </tr>
    <!-- CONTENT -->
    <tr>
        <td style="min-width: 590px;">
            <table border="0" cellpadding="0" cellspacing="0" width="590" style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
                <tr>
                    <td valign="top" style="font-size: 13px;">
                        <div>
                            Hi <t t-out="object.partner_id and object.partner_id.name or ''">Deco Addict</t>,<br/><br/>
                            Welcome to <t t-out="object.company_id.name or ''">My Company (San Francisco)</t>.
                            It's great to meet you! Now that you're on board, you'll discover what <t t-out="object.company_id.name or ''">My Company (San Francisco)</t> has to offer. My name is <t t-out="object.user_id.name or ''">Marc Demo</t> and I'll help you get the most out of Odoo. Could we plan a quick demo soon?<br/>
                            Feel free to reach out at any time!<br/><br/>
                            Best,<br/>
                            <t t-if="object.user_id">
                                <b><t t-out="object.user_id.name or ''">Marc Demo</t></b>
                                <br/>Email: <t t-out="object.user_id.email or ''"><EMAIL></t>
                                <br/>Phone: <t t-out="object.user_id.phone or ''">******-123-4567</t>
                            </t>
                            <t t-else="">
                                <t t-out="object.company_id.name or ''">My Company (San Francisco)</t>
                            </t>
                        </div>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <!-- FOOTER -->
    <tr>
        <td align="center" style="min-width: 590px; padding: 0 8px 0 8px; font-size:11px;">
            <hr width="100%" style="background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 4px 0px;"/>
            <b t-out="object.company_id.name or ''">My Company (San Francisco)</b><br/>
            <div style="color: #999999;">
                <t t-out="object.company_id.phone or ''">******-123-4567</t>
                <t t-if="object.company_id.email">
                    | <a t-attf-href="'mailto:%s' % {{ object.company_id.email }}" style="text-decoration:none; color: #999999;" t-out="object.company_id.email or ''"><EMAIL></a>
                </t>
                <t t-if="object.company_id.website">
                    | <a t-attf-href="'%s' % {{ object.company_id.website }}" style="text-decoration:none; color: #999999;" t-out="object.company_id.website or ''">http://www.example.com</a>
                </t>
            </div>
        </td>
    </tr>
</tbody>
</table>
</td></tr>
<!-- POWERED BY -->
<tr><td align="center" style="min-width: 590px;">
    Powered by <a target="_blank" href="https://www.odoo.com?utm_source=db&amp;utm_medium=email" style="color: #875A7B;">Odoo</a>
</td></tr>
</table>
        </field>
        <field name="lang">{{ object.partner_id.lang }}</field>
        <field name="auto_delete" eval="True"/>
    </record>

</data></odoo>
