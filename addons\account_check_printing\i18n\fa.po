# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_check_printing
# 
# Translators:
# zakariya moradi, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Tiffany <PERSON>, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
msgid "%(journal)s: Check Number Sequence"
msgstr ""

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "<span>&amp;nbsp;</span>"
msgstr "<span>&amp;nbsp;</span>"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid ""
"Adjust the margins of generated checks to make it fit your printer's "
"settings."
msgstr "حاشیه چک های ایجاد شده را متناسب با تنظیمات چاپگر خود تنظیم کنید."

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_amount_in_words
msgid "Amount in Words"
msgstr "مقدار به حروف"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid "Bills"
msgstr "صورتحساب"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Cancel"
msgstr "لغو"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__bank_check_printing_layout
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid "Check Layout"
msgstr "قالب چک"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_left
msgid "Check Left Margin"
msgstr "حاشیه چپ چک"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_number
msgid "Check Number"
msgstr "شماره چک"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Check Printing"
msgstr "چاپ چک"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_right
msgid "Check Right Margin"
msgstr "حاشیه راست چک"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_sequence_id
msgid "Check Sequence"
msgstr "توالی چک"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid "Check Top Margin"
msgstr "حاشیه بالا چک"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid "Check numbers can only consist of digits"
msgstr "اعداد وارد شده در چک فقط می توانند رقم باشند"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr ""
"اگر چک های از قبل چاپ شده شما شماره گذاری نشده است ، این گزینه را علامت "
"بزنید."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Check to print"
msgstr "چک برای پرینت"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: model:account.payment.method,name:account_check_printing.account_payment_method_check
msgid "Checks"
msgstr "چکها"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_sequence_id
msgid "Checks numbering sequence."
msgstr "توالی شماره گذاری چک ها"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_payment_check_printing_search
msgid "Checks to Print"
msgstr "چکهای برای پرینت"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Checks to print"
msgstr "چکهای برای پرینت"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Default"
msgstr "پیش‌فرض"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid "Go to the configuration panel"
msgstr "برو به صفحه پیکربندی"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__id
msgid "ID"
msgstr "شناسه"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr ""
"برای چاپ همزمان چند فقره چک ، آنها باید به یک دفتر بانکی تعلق داشته باشند."

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_journal
msgid "Journal"
msgstr "روزنامه"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_uid
msgid "Last Updated by"
msgstr "آخرین بروز رسانی توسط"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Manual Numbering"
msgstr "شماره دهی دستی"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Providers: Each payment provider has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""
"دستی: پرداخت یا واریز با هر روشی غیر از اودوو.\n"
"درگاه‌های پرداخت: هر درگاه روش پرداخت مخصوص به خود دارد. با کمک توکن پرداخت ذخیره شده توسط شریک هنگام خرید یا اشتراک آنلاین، درخواست تراکنش از/به یک کارت را ارائه دهید. \n"
"چک: پرداخت صورت‌حساب‌ها و پرینت آن از اودوو.\n"
"واریز انبوه: جمع‌آوری چک‎‌های چند مشتری و سپس ارائه‌ی مجموع آنها برای واریز به حساب. ماژول پرداخت انبوه برای این کار موردنیاز است. \n"
"انتقال اعتباری SEPA: پرداخت در حوزه‌ی SEPA با ارائه‎‌ی یک فایل اعتباری SEPA به بانک. مآژول حساب SAPA برای این کار لازم است. \n"
"حق برداشت مستقیم از SEPA: پرداخت از طریق حوزه‌ی SEPA با کمک وکالت‌نامه‌ای که شریک‌تان به شما می‌دهد. استفاده از ماژول حساب SEPA ضروری است. \n"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid "Multi-Pages Check Stub"
msgstr "ته چک چند صفحه ای"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_next_number
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__next_check_number
msgid "Next Check Number"
msgstr "شماره چک بعدی"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: code:addons/account_check_printing/wizard/print_prenumbered_checks.py:0
msgid "Next Check Number should only contains numbers."
msgstr "شماره چک بعدی فقط باید شامل اعداد باشد."

#. module: account_check_printing
#: model:ir.model.fields.selection,name:account_check_printing.selection__res_company__account_check_printing_layout__disabled
msgid "None"
msgstr "هیچکدام"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__payment_method_line_id
msgid "Payment Method"
msgstr "روش پرداخت"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment_method
msgid "Payment Methods"
msgstr "روشهای پرداخت"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment
msgid "Payments"
msgstr "پرداخت‌ها"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"Payments to print as a checks must have 'Check' selected as payment method "
"and not have already been reconciled"
msgstr ""
"پرداخت های چاپی به عنوان چک باید \"چک\" به عنوان روش پرداخت انتخاب شده باشد "
"و قبلاً مغایرت گیری نشده باشد"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"Please enter the number of the first pre-printed check that you are about to"
" print on."
msgstr ""
"لطفاً شماره اولین چک از قبل چاپ شده را که می خواهید روی آن چاپ کنید وارد "
"کنید."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print"
msgstr "چاپ"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Print Check"
msgstr "چاپ چک"

#. module: account_check_printing
#: model:ir.actions.server,name:account_check_printing.action_account_print_checks
msgid "Print Checks"
msgstr "چاپ چکها"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid "Print Date Label"
msgstr "چاپ برچسب تاریخ"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: model:ir.model,name:account_check_printing.model_print_prenumbered_checks
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print Pre-numbered Checks"
msgstr "چاپ چکهای از پیش شماره‌گذاری شده"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid "Refunds"
msgstr "برگشت از فروش ها"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_right
msgid "Right Margin"
msgstr "حاشیه راست"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid ""
"Select the format corresponding to the check paper you will be printing your checks on.\n"
"In order to disable the printing feature, select 'None'."
msgstr ""
"قالب مربوط به برگه چک را که روی آن چاپ می کنید ، انتخاب کنید.\n"
"برای غیرفعال کردن ویژگی چاپ ، \"هیچکدام\" را انتخاب کنید."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Sent"
msgstr "ارسال"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_next_number
msgid "Sequence number of the next printed check."
msgstr "شماره ترتیب چک چاپی بعدی."

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__show_check_number
msgid "Show Check Number"
msgstr "شماره‌ی چک را نشان بده"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"Something went wrong with Check Layout, please select another layout in "
"Invoicing/Accounting Settings and try again."
msgstr ""
"بررسی طراح دچار مشکل شده است، لطفاً طرح دیگری را در بخش تنظیمات  صدور "
"فاکتور/حسابداری انتخاب کرده و دوباره امتحان کنید."

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"The following numbers are already used:\n"
"%s"
msgstr "اعداد زیر قبلاً استفاده شده‌اند: %s"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
msgid ""
"The last check number was %s. In order to avoid a check being rejected by "
"the bank, you can only use a greater number."
msgstr ""
"شماره‌‎ی آخرین چک %s بود. به منظور اجتناب از رد شدن یک چک توسط بانکف تنها "
"می‌توانید از یک عدد بزرگ‌تر استفاده کنید."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_number
msgid ""
"The selected journal is configured to print check numbers. If your pre-"
"printed check paper already has numbers or if the current numbering is "
"wrong, you can change it in the journal configuration page."
msgstr ""
"دفتر روزنامه‌ی انتخاب شده برای چاپ شماره‌های چک پیکربندی شده است. چکی که از "
"قب چاتپ شده است دارای شماره است یا اگر شماره‌ی جدید نادرست است، می‌توانید آن"
" را در صفحه‌ی پیکربندی دفتر روزنامه تغییر دهید."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid ""
"This option allows you to print check details (stub) on multiple pages if "
"they don't fit on a single page."
msgstr ""
"اگر جزئیات چک (ته چک) در یک صفحه جای نگیرند، این گزینه به شما امکان می‌دهد "
"آنها را روی چند صفحه چاپ کنید."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid ""
"This option allows you to print the date label on the check as per CPA.\n"
"Disable this if your pre-printed check includes the date label."
msgstr ""
"این گزینه به شما امکان می‌دهد برچسب تاریخ روی چک را برای هر CPA چاپ کنید.\n"
"اگر این چک از قبل چاپ شده دارای برچسب تاریخ باشد، آن را لغو کنید."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"This will allow to save on payments the number of the corresponding check."
msgstr ""
"این گزینه امکان ذخیره‌ی شماره‌ی چک‌های مربوطه را در پرداخت‌ها فراهم می‌کند. "

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Unmark Sent"
msgstr "ارسال نشده"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Void Check"
msgstr "چک باطل شده"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"You have to choose a check layout. For this, go in Invoicing/Accounting "
"Settings, search for 'Checks layout' and set one."
msgstr ""
"شما باید یک طرح چک را انتخاب کنید. برای این کار، به تنظیمات صدور "
"فاکتور/حسابداری رفته و گزینه‌ی «طرح چک» را جستجو کنید."
