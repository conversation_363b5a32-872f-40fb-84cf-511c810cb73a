# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_contract
# 
# Translators:
# Abe Manyo, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_holidays_contract
#. odoo-python
#: code:addons/hr_holidays_contract/models/hr_leave.py:0
msgid ""
"A leave cannot be set across multiple contracts with different working schedules.\n"
"\n"
"Please create one time off for each contract.\n"
"\n"
"Time off:\n"
"%(time_off)s\n"
"\n"
"Contracts:\n"
"%(contracts)s"
msgstr ""
"Cuti tidak dapat ditetapkan di lebih dari satu kontrak dengan jadwal kerja yang berbeda.\n"
"\n"
"Silakan buat satu cuti untuk setiap kontrak.\n"
"\n"
"Cuti:\n"
"%(time_off)s\n"
"\n"
"Kontrak:\n"
"%(contracts)s"

#. module: hr_holidays_contract
#: model:ir.model,name:hr_holidays_contract.model_hr_employee_base
msgid "Basic Employee"
msgstr "Karyawan Dasar"

#. module: hr_holidays_contract
#. odoo-python
#: code:addons/hr_holidays_contract/models/hr_contract.py:0
msgid ""
"Changing the contract on this employee changes their working schedule in a "
"period they already took leaves. Changing this working schedule changes the "
"duration of these leaves in such a way the employee no longer has the "
"required allocation for them. Please review these leaves and/or allocations "
"before changing the contract."
msgstr ""
"Mengubah kontrak pada karyawan ini mengubah jadwal kerja mereka menjadi "
"periode di mana mereka sudah mengambil cuti. Mengubah jadwal kerja ini "
"mengubah durasi cuti tersebut sehingga karyawan tidak lagi memiliki alokasi "
"yang diperlukan untuk cuti tersebut. Mohon tinjau cuti tersebut dan/atau "
"alokasi sebelum mengubah kontrak."

#. module: hr_holidays_contract
#: model:ir.model,name:hr_holidays_contract.model_hr_contract
msgid "Employee Contract"
msgstr "Kontrak Karyawan"

#. module: hr_holidays_contract
#: model:ir.model,name:hr_holidays_contract.model_hr_leave
msgid "Time Off"
msgstr "Cuti"
