<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="badge_good_job" model="gamification.badge">
            <field name="name">Good Job</field>
            <field name="description">You did great at your job.</field>
            <field name="rule_auth">everyone</field>
            <field name="image_1920" type="base64" file="gamification/static/img/badge_good_job-image.png"/>
        </record>

        <record id="badge_problem_solver" model="gamification.badge">
            <field name="name">Problem Solver</field>
            <field name="description">No one can solve challenges like you do.</field>
            <field name="rule_auth">everyone</field>
            <field name="image_1920" type="base64" file="gamification/static/img/badge_problem_solver-image.png"/>
        </record>

        <record id="badge_hidden" model="gamification.badge">
            <field name="name">Hidden</field>
            <field name="description">You have found the hidden badge</field>
            <field name="rule_auth">nobody</field>
            <field name="image_1920" type="base64" file="gamification/static/img/badge_hidden-image.png"/>
            <field name="active" eval="False" />
        </record>

        <record id="badge_idea" model="gamification.badge">
            <field name="name">Brilliant</field>
            <field name="description">With your brilliant ideas, you are an inspiration to others.</field>
            <field name="rule_auth">everyone</field>
            <field name="rule_max">True</field>
            <field name="rule_max_number">2</field>
            <field name="image_1920" type="base64" file="gamification/static/img/badge_idea-image.png"/>
        </record>
    </data>
</odoo>
