# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_password_policy
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: auth_password_policy
#: model:ir.model.fields,field_description:auth_password_policy.field_res_config_settings__minlength
msgid "Minimum Password Length"
msgstr "الحد الأدنى لطول كلمة المرور"

#. module: auth_password_policy
#: model:ir.model.fields,help:auth_password_policy.field_res_config_settings__minlength
msgid ""
"Minimum number of characters passwords must contain, set to 0 to disable."
msgstr ""
"الحد الأدنى لعدد الأحرف التي يجب أن تحتويها كلمات المرور. ضع 0 للتعطيل. "

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_field.js:0
msgid "Password"
msgstr "كلمة المرور"

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_meter.js:0
msgid ""
"Required: %s\n"
"\n"
"Hint: to increase password strength, increase length, use multiple words, and use non-letter characters."
msgstr ""
"مطلوب: %s \n"
"\n"
"تلميح: لجعل كلمة المرور قوية أكثر، زد من طولها واستخدم عدة كلمات واستخدم رموزاً غير الأخرف. "

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_users
msgid "User"
msgstr "المستخدم"

#. module: auth_password_policy
#. odoo-python
#: code:addons/auth_password_policy/models/res_users.py:0
msgid ""
"Your password must contain at least %(minimal_length)d characters and only "
"has %(current_count)d."
msgstr ""
"يجب أن تحتوي كلمة المرور الخاصة بك على %(minimal_length)d رموز على الأقل "
"والحالية مكونة من%(current_count)d فقط. "

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_policy.js:0
msgid "at least %s character classes"
msgstr "%s فئات رموز على الأقل "

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_policy.js:0
msgid "at least %s characters"
msgstr "%s رموز على الأقل "

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_policy.js:0
msgid "at least %s words"
msgstr "%s كلمات على الأقل "
