# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays
# 
# Translators:
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid " to %(date_to_utc)s"
msgstr " から %(date_to_utc)sまで"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important &gt;&lt;/td&gt;"
msgstr "!important &gt;&lt;/td&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important /&gt;"
msgstr "!important /&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important/&gt;"
msgstr "!important/&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 10px\" &gt;"
msgstr "!important; font-size: 10px\" &gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 8px; min-width: 18px\"&gt;"
msgstr "!important; font-size: 8px; min-width: 18px\"&gt;"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "%(allocation_name)s (from %(date_from)s to %(date_to)s)"
msgstr "%(allocation_name)s ( %(date_from)s から %(date_to)sまで)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "%(allocation_name)s (from %(date_from)s to No Limit)"
msgstr "%(allocation_name)s (%(date_from)s から無期限)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(employee)s on Time Off : %(duration)s"
msgstr "%(employee)s 休暇中 : %(duration)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(employee_name)s - from %(date_from)s to %(date_to)s - %(state)s"
msgstr "%(employee_name)s -  %(date_from)s から %(date_to)s まで- %(state)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(holiday_name)s has been refused."
msgstr "%(holiday_name)sは否認されました。 "

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"%(leave_name)s has been cancelled with the justification: <br/> %(reason)s."
msgstr "%(leave_name)sは以下の理由で取消されました: <br/> %(reason)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(leave_type)s: %(duration)s (%(start)s)"
msgstr "%(leave_type)s: %(duration)s (%(start)s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_leave_allocation_generate_multi_wizard.py:0
msgid "%(name)s (%(duration)s %(request_unit)s(s))"
msgstr "%(name)s (%(duration)s %(request_unit)s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "%(name)s (%(duration)s day(s))"
msgstr "%(name)s (%(duration)s 日)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "%(name)s (%(duration)s hour(s))"
msgstr "%(name)s (%(duration)s 時間)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid "%(name)s (%(time)g remaining out of %(maximum)g days)"
msgstr "%(name)s (残%(time)g、全%(maximum)g 日のうち)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid "%(name)s (%(time)g remaining out of %(maximum)g hours)"
msgstr "%(name)s (残%(time)g、全%(maximum)g 時間のうち)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(name)s: %(duration)s"
msgstr "%(name)s: %(duration)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(person)s on %(leave_type)s: %(duration)s (%(start)s)"
msgstr "%(person)s %(leave_type)s中: %(duration)s (%(start)s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(person)s: %(duration)s (%(start)s)"
msgstr "%(person)s: %(duration)s (%(start)s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid "%s (copy)"
msgstr "%s (コピー)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%s: Time Off"
msgstr "%s: 休暇"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&gt;"
msgstr "&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/td&gt;"
msgstr "&lt;/td&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/th&gt;"
msgstr "&lt;/th&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"
msgstr ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;td style=background-color:"
msgstr "&lt;td style=background-color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;th class=\"text-center\" colspan="
msgstr "&lt;th class=\"text-center\" colspan="

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "(valid until"
msgstr "(有効期限"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "- Valid for"
msgstr "- 有効:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "00:00"
msgstr "00:00"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-check\"/> Validate"
msgstr "<i class=\"fa fa-check\"/> 検証"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual' or state != "
"'confirm'\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual' or state != "
"'confirm'\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual'\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual'\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "<i class=\"fa fa-long-arrow-right\" title=\"to\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"to\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-thumbs-up\"/> Approve"
msgstr "<i class=\"fa fa-thumbs-up\"/> 承認"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-times\"/> Refuse"
msgstr "<i class=\"fa fa-times\"/> 否認"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
msgid ""
"<span class=\"ml8\" invisible=\"request_unit == 'hour'\">Days</span>\n"
"                            <span class=\"ml8\" invisible=\"request_unit != 'hour'\">Hours</span>"
msgstr ""
"<span class=\"ml8\" invisible=\"request_unit == 'hour'\">日</span>\n"
"                            <span class=\"ml8\" invisible=\"request_unit != 'hour'\">時間</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<span class=\"ml8\" invisible=\"type_request_unit == 'hour'\">Days</span>\n"
"                                <span class=\"ml8\" invisible=\"type_request_unit != 'hour'\">Hours</span>"
msgstr ""
"<span class=\"ml8\" invisible=\"type_request_unit == 'hour'\">日</span>\n"
"                                <span class=\"ml8\" invisible=\"type_request_unit != 'hour'\">時間</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Back On\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            戻り予定:\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Time Off\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            休暇\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                           Time Off\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                           休暇\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Accruals</span>"
msgstr "<span class=\"o_stat_text\">休暇の累積</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Allocations</span>"
msgstr "<span class=\"o_stat_text\">割当</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Time Off</span>"
msgstr "<span class=\"o_stat_text\">休暇</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\"> to </span>"
msgstr "<span class=\"text-muted\"> から </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\">from </span>"
msgstr "<span class=\"text-muted\">開始</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span>The employee has a different timezone than yours! Here dates and times are displayed in the employee's timezone</span>\n"
"                    ("
msgstr ""
"<span>従業員のタイムゾーンはあなたのタイムゾーンと異なります！ここでは、日付と時間が従業員のタイムゾーンで表示されます。</span>\n"
"                    ("

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span>You can only take this time off in whole days, so if your schedule has"
" half days, it won't be used efficiently.</span>"
msgstr "<span>この休暇は丸一日単位でしか取れないため、半日単位のスケジュールだと効率的に利用することができません。</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "<strong>Departments and Employees</strong>"
msgstr "<strong>部署と従業員</strong>"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "A cancelled leave cannot be modified."
msgstr "取消済の休暇は変更できません。"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr "従業員のPTO、病欠、および承認ステータスを追跡するための優れた方法。"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid ""
"A great way to keep track on your time off requests, sick days, and approval"
" status."
msgstr "休暇申請、病欠、承認状況を追跡するのに最適な方法です。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "A time off cannot be duplicated."
msgstr "休暇を複製することはできません。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__show_leaves
msgid "Able to see Remaining Time Off"
msgstr "残りの休みを見ることができます"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid "Absence"
msgstr "不在"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__absence_of_today
msgid "Absence by Today"
msgstr "本日で不在"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid ""
"Absent Employee(s), Whose time off requests are either confirmed or "
"validated on today"
msgstr "不在の従業員、その休暇申請は本日確認または検証されます"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_employee_action_from_department
msgid "Absent Employees"
msgstr "不在の従業員"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__is_absent
msgid "Absent Today"
msgstr "本日不在"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Accrual (Future):"
msgstr "累積休暇 (今後):"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__accrual
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_type__accrual
msgid "Accrual Allocation"
msgstr "発生割当"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Accrual Level"
msgstr "休暇の累積レベル"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_plan
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_plan_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__accrual_plan_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__accrual_plan_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Accrual Plan"
msgstr "休暇累積プラン"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_level
msgid "Accrual Plan Level"
msgstr "休暇累積プランレベル"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
msgid "Accrual Plan's Employees"
msgstr "休暇累積プランの従業員"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_accrual_plans
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_accrual_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_tree
msgid "Accrual Plans"
msgstr "休暇累積プラン"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.hr_leave_allocation_cron_accrual_ir_actions_server
msgid "Accrual Time Off: Updates the number of time off"
msgstr "見越休暇:休暇の数を更新します"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_validity
msgid "Accrual Validity"
msgstr "繰越休暇の有効性"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_validity_count
msgid "Accrual Validity Count"
msgstr "累積有効数"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_validity_type
msgid "Accrual Validity Type"
msgstr "累積有効タイプ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accruals_ids
msgid "Accruals"
msgstr "休暇累積"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accrual_count
msgid "Accruals count"
msgstr "休暇累積数"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrued_gain_time
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__accrued_gain_time
msgid "Accrued Gain Time"
msgstr "累積取得時間"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__active
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__active
msgid "Active"
msgstr "アクティブ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Allocations"
msgstr "アクティブな割り当て"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__active_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__active_employee
msgid "Active Employee"
msgstr "有効な従業員"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
msgid "Active but on leave"
msgstr "有効だが休暇中"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_ids
msgid "Activities"
msgstr "活動"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "例外活動文字装飾"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_state
msgid "Activity State"
msgstr "活動状態"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動種別アイコン"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.mail_activity_type_action_config_hr_holidays
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_config_activity_type
msgid "Activity Types"
msgstr "活動タイプ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Add a description..."
msgstr "説明を追加..."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Add a reason..."
msgstr "理由を記述してください..."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Add some description for the people that will validate it"
msgstr "それを検証する人用に説明を追加します"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__added_value_type
msgid "Added Value Type"
msgstr "追加値タイプ"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_manager
msgid "Administrator"
msgstr "管理者"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__end_of_accrual
msgid "After this accrual's period"
msgstr "この休暇累積期間後"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__pm
msgid "Afternoon"
msgstr "午後"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_all
msgid "All Allocations"
msgstr "全ての休暇割当"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_dashboard
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_action_approve_department
msgid "All Time Off"
msgstr "全ての休暇"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__all
msgid "All accrued time carried over"
msgstr "全ての累積時間を繰越"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__last_several_days
msgid "All day"
msgstr "終日"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Allocated ("
msgstr "割当済 ("

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__duration_display
msgid "Allocated (Days/Hours)"
msgstr "割り当て済み(日/時間)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__allocation_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__duration
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Allocation"
msgstr "割当"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_approval
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
msgid "Allocation Approval"
msgstr "割り当ての承認"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_display
msgid "Allocation Display"
msgstr "割り当て表示"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__allocation_mode
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__allocation_mode
msgid "Allocation Mode"
msgstr "割当モード"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_notif_subtype_id
msgid "Allocation Notification Subtype"
msgstr "割当通知サブタイプ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_remaining_display
msgid "Allocation Remaining Display"
msgstr "割当残表示"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/wizard/hr_leave_allocation_generate_multi_wizard.py:0
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__allocation_id
#: model:mail.message.subtype,description:hr_holidays.mt_leave_allocation
#: model:mail.message.subtype,name:hr_holidays.mt_leave_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Allocation Request"
msgstr "割当申請"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Allocation Requests"
msgstr "割当申請"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_second_approval
msgid "Allocation Second Approval"
msgstr "休暇割当2次承認"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__allocation_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__allocation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Allocation Type"
msgstr "割当タイプ"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"Allocation must be confirmed \"To Approve\" or validated once \"Second "
"Approval\" in order to approve it."
msgstr "承認するためには、休暇割当が確認済 \"承認待ち\" または 1度検証済 \"2次承認\" でなければなりません。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Allocation of %(leave_type)s: %(amount).2f %(unit)s to %(target)s"
msgstr " %(leave_type)sの割当: %(amount).2f %(unit)s から %(target)sへ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__week_day
msgid "Allocation on"
msgstr "以下での割当"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"Allocation request must be confirmed, second approval or validated in order "
"to refuse it."
msgstr "休暇申請を拒否するには、確認済、２次承認または検証済である必要があります。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Allocation state must be \"Refused\" in order to be reset to \"To Approve\"."
msgstr "\"否認\"に再設定するには、休暇割当ステータスが \"承認待ち\"である必要があります。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__allocation_to_approve_count
msgid "Allocation to Approve"
msgstr "承認待ちの割当申請"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_approve_department
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_count
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_manager_approve_allocations
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Allocations"
msgstr "割当"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allows_negative
msgid "Allow Negative Cap"
msgstr "負の限度を許可"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Allow To Attach Supporting Document"
msgstr "参考資料の添付を許可"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__allocation_mode
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_generate_multi_wizard__allocation_mode
msgid ""
"Allow to create requests in batchs:\n"
"- By Employee: for a specific employee\n"
"- By Company: all employees of the specified company\n"
"- By Department: all employees of the specified department\n"
"- By Employee Tag: all employees of the specific employee group category"
msgstr ""
"申請をバッチで作成できるようにします。\n"
"-従業員別:特定の従業員用\n"
"-会社別:指定した会社のすべての従業員\n"
"-部門別:指定した部門のすべての従業員\n"
"-従業員別タグ:特定の従業員グループカテゴリのすべての従業員"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__already_accrued
msgid "Already Accrued"
msgstr "既に累積済です"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Amount"
msgstr "金額"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "An employee already booked time off which overlaps with this period:%s"
msgstr "従業員が以下の期間と重複する休暇を予約しています: %s"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Analyze from"
msgstr "分析元"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_evaluation_report_graph
msgid "Appraisal Analysis"
msgstr "査定分析"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_validation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Approval"
msgstr "承認"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/views/calendar/common/calendar_common_popover.xml:0
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Approve"
msgstr "承認"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.ir_actions_server_approve_allocations
msgid "Approve Allocations"
msgstr "割り当てを承認する"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__approved
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_dashboard_new_time_off
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Approved"
msgstr "承認済"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Approved Requests"
msgstr "承認済"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Approved:"
msgstr "承認済:"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__apr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__apr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__apr
msgid "April"
msgstr "4月"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Archived"
msgstr "アーカイブ済"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
msgid "Are you sure you want to delete this record?"
msgstr "本当にこのレコードを削除しますか？"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "Arrow"
msgstr "矢印"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "Arrow icon"
msgstr "矢印アイコン"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__allocation
msgid "At the allocation date"
msgstr "割当日において"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__accrued_gain_time__end
msgid "At the end of the accrual period"
msgstr "累積休暇期間終了時"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__accrued_gain_time__start
msgid "At the start of the accrual period"
msgstr "累積休暇期間開始時"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__year_start
msgid "At the start of the year"
msgstr "年始に"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "At work"
msgstr "勤務中"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids
msgid "Attach File"
msgstr "ファイルを添付"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_attachment_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__attachment_ids
msgid "Attachments"
msgstr "添付"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__aug
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__aug
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__aug
msgid "August"
msgstr "8月"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_leave_generate_multi_wizard.py:0
msgid ""
"Automatic time off spliting during batch generation is not managed for ovelapping time off declared in hours. Conflicting time off:\n"
"%s"
msgstr ""
"バッチ生成中の自動休暇分割が、時間単位で宣言された休暇の重複に対して管理されません。競合する休暇:\n"
"%s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Available"
msgstr "利用可能"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__remaining_leaves
msgid "Available Time Off Days"
msgstr "利用可能休暇日数"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Available:"
msgstr "利用可能:"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
msgid "Away"
msgstr "外出"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Back On"
msgstr "戻り予定:"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/persona_model_patch.js:0
msgid "Back on %s"
msgstr "戻り予定: %s"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_balance
msgid "Balance"
msgstr "残高"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "Balance at the"
msgstr "以下での収支"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__is_based_on_worked_time
msgid "Based on worked time"
msgstr "労働時間に基づく"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee_base
msgid "Basic Employee"
msgstr "基本社員"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__both
msgid "Both Approved and Confirmed"
msgstr "承認・確認済"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_mode__company
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_generate_multi_wizard__allocation_mode__company
msgid "By Company"
msgstr "会社別"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_mode__department
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_generate_multi_wizard__allocation_mode__department
msgid "By Department"
msgstr "部門別"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_mode__employee
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_generate_multi_wizard__allocation_mode__employee
msgid "By Employee"
msgstr "従業員別"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_mode__category
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_generate_multi_wizard__allocation_mode__category
msgid "By Employee Tag"
msgstr "従業員タグ別"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__manager
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__manager
msgid "By Employee's Approver"
msgstr "従業員の承認者による"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__both
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__both
msgid "By Employee's Approver and Time Off Officer"
msgstr "従業員の承認者または休暇担当者による"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__hr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__hr
msgid "By Time Off Officer"
msgstr "休暇担当者による"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_calendar_event
msgid "Calendar Event"
msgstr "カレンダイベント"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_approve
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__can_approve
msgid "Can Approve"
msgstr "承認できます"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_cancel
msgid "Can Cancel"
msgstr "取消可能"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__can_modify_value_type
msgid "Can Modify Value Type"
msgstr "値タイプ調整可能"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_reset
msgid "Can reset"
msgstr "リセット可能"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Cancel"
msgstr "キャンセル"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/static/src/views/hooks.js:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
msgid "Cancel Time Off"
msgstr "休暇を取消"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_cancel_leave
msgid "Cancel Time Off Wizard"
msgstr "休暇ウィザード取消"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__cancel
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Cancelled"
msgstr "取消済"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Cancelled Time Off"
msgstr "キャンセル済休暇"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time
msgid "Cap accrued time"
msgstr "累積時間を制限"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Cap:"
msgstr "制限:"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__carried_over_days_expiration_date
msgid "Carried over days expiration date"
msgstr "繰越日の有効期限日"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Carry Over Validity"
msgstr "繰越日有効性"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__action_with_unused_accruals
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Carry over"
msgstr "繰越す"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__maximum
msgid "Carry over with a maximum"
msgstr "最大で繰越"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Carry over:"
msgstr "繰越:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Carry-Over Date"
msgstr "繰越日"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_date
msgid "Carry-Over Time"
msgstr "繰越時間"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_day
msgid "Carryover Day"
msgstr "繰越日"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_day_display
msgid "Carryover Day Display"
msgstr "繰越日表示"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_month
msgid "Carryover Month"
msgstr "繰越月"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee_base.py:0
msgid ""
"Changing this working schedule results in the affected employee(s) not "
"having enough leaves allocated to accomodate for their leaves already taken "
"in the future. Please review this employee's leaves and adjust their "
"allocation accordingly."
msgstr ""
"この勤務予定を変更すると、影響を受ける従業員は、将来、すでに取得した休暇に見合うだけの休暇が割当てられなくなります。この従業員の休暇を確認し、それに応じて割当てを調整して下さい。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Choose a cap for this accrual."
msgstr "この累積休暇の制限を選択"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__responsible_ids
msgid ""
"Choose the Time Off Officers who will be notified to approve allocation or "
"Time Off Request. If empty, nobody will be notified"
msgstr "割当または休暇申請を承認するために通知される休暇担当者を選択します。空白の場合、誰にも通知されません。"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Click on any date or on this button to request a time-off"
msgstr "任意の日付またはこのボタンをクリックして、休暇を申請して下さい。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__color
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__color
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Color"
msgstr "色"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__company_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Company"
msgstr "会社"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_comp
msgid "Compensatory Days"
msgstr "振替休日"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Configuration"
msgstr "設定"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
msgid "Confirmation"
msgstr "確認"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__confirmed
msgid "Confirmed"
msgstr "確認済"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
msgid "Confirmed and Approved"
msgstr "確認済および承認済"

#. module: hr_holidays
#: model_terms:web_tour.tour,rainbow_man_message:hr_holidays.hr_holidays_tour
msgid "Congrats, we can see that your request has been validated."
msgstr "おめでとうございます。あなたの申請は承認されました。"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_partner
msgid "Contact"
msgstr "連絡先"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid ""
"Count of allocations for this time off type (approved or waiting for "
"approbation) with a validity period starting this year."
msgstr "今年から有効な、この休暇タイプ(承認済みまたは承認待ち)の割当数。"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Count of plans linked to this time off type."
msgstr "この休暇タイプにリンクされている計画数。"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid ""
"Count of time off requests for this time off type (approved or waiting for "
"approbation) with a start date in the current year."
msgstr "この休暇タイプ(承認済または承認待ち)の当年度開始日の休暇申請数。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__country_id
msgid "Country"
msgstr "国"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__country_code
msgid "Country Code"
msgstr "国コード"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__icon_id
msgid "Cover Image"
msgstr "カバーイメージ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
msgid "Create Allocations"
msgstr "割当を作成"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
msgid "Create a new time off allocation"
msgstr "新しい休暇割当を作成する"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid "Create a new time off allocation request"
msgstr "新しい休暇割当申請を作成します"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_uid
msgid "Created by"
msgstr "作成者"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_date
msgid "Created on"
msgstr "作成日"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__current_leave_state
msgid "Current Time Off Status"
msgstr "現在のオフステータス"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_id
msgid "Current Time Off Type"
msgstr "現在の休暇タイプ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Current Year"
msgstr "今年"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Currently Valid"
msgstr "現在有効"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_hours
msgid "Custom Hours"
msgstr "カスタム時間"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__daily
msgid "Daily"
msgstr "日次"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_new_request
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_new_request
msgid "Dashboard"
msgstr "ダッシュボード"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Date"
msgstr "日付"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from_period
msgid "Date Period Start"
msgstr "日付期間開始"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__lastcall
msgid "Date of the last accrual allocation"
msgstr "累積休暇の最終割当日"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__nextcall
msgid "Date of the next accrual allocation"
msgstr "次の見越配分の日付"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Dates"
msgstr "日付"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__day
msgid "Day"
msgstr "日"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__accrual_validity_type__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__added_value_type__day
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Days"
msgstr "日"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__dec
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__dec
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__dec
msgid "December"
msgstr "12月"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__max_allowed_negative
msgid ""
"Define the maximum level of negative days this kind of time off can reach. "
"Value must be at least 1."
msgstr "この種の休暇のマイナス日数の最大レベルを定義します。値は少なくとも1である必要があります。"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Delete"
msgstr "削除"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_department
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__department_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Department"
msgstr "部門"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Department search"
msgstr "部門検索"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__department_ids
msgid "Departments"
msgstr "部門"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "退職ウィザード"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__description
msgid "Description"
msgstr "説明"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name_validity
msgid "Description with validity"
msgstr "有効性のある説明"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "Discard"
msgstr "破棄"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__display_name
msgid "Display Name"
msgstr "表示名"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Display Option"
msgstr "表示オプション"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_calendar_meeting
msgid "Display Time Off in Calendar"
msgstr "カレンダーに休暇を表示"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
msgid ""
"Due to a change in global time offs, %s extra day(s) have been taken from "
"your allocation. Please review this leave if you need it to be changed."
msgstr "全体休暇の変更により、あなたの割当てから追加の休暇%sが取得されました。変更が必要な場合は、この休暇を見直して下さい。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
msgid ""
"Due to a change in global time offs, this leave no longer has the required "
"amount of available allocation and has been set to refused. Please review "
"this leave."
msgstr "全体休暇の変更により、この休暇は必要な割当がなくなり否認に設定されました。この休暇を見直して下さい。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
msgid ""
"Due to a change in global time offs, you have been granted %s day(s) back."
msgstr "全体休暇の変更により、%s日の休暇が付与されました。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__duration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Duration"
msgstr "期間"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_graph
msgid "Duration (Days)"
msgstr "休暇期間（日）"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_graph
msgid "Duration (Hours)"
msgstr "所要時間（時間）"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days_display
msgid "Duration (days)"
msgstr "休暇期間（日）"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid "Duration (hours)"
msgstr "期間(時間)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days
msgid "Duration in days. Reference field to use when necessary."
msgstr "日単位の期間。必要に応じて使用する参照フィールド。"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
msgid "Edit Allocation"
msgstr "割り当ての編集"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Edit Time Off"
msgstr "休暇の編集"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__employee_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Employee"
msgstr "従業員"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__active_employee
msgid "Employee Active"
msgstr "アクティブな従業員"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_company_id
msgid "Employee Company"
msgstr "従業員会社"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__employee_requests
msgid "Employee Requests"
msgstr "従業員の申請"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__category_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__category_id
msgid "Employee Tag"
msgstr "従業員タグ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Employee accrue"
msgstr "従業員の累積"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban_approve_department
msgid "Employee's image"
msgstr "従業員画像"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__emp
msgid "Employee(s)"
msgstr "従業員"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__employees_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__employee_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__employee_ids
msgid "Employees"
msgstr "従業員"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Employees Off Today"
msgstr "本日休暇中の従業員"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__end_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_to
msgid "End Date"
msgstr "終了日"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__yes
msgid "Extra Days Requests Allowed"
msgstr "追加日数申請可"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__employee_requests
msgid ""
"Extra Days Requests Allowed: User can request an allocation for himself.\n"
"\n"
"        Not Allowed: User cannot request an allocation."
msgstr ""
"追加日数の申請が可能： ユーザ自身が割当を申請することができます。\n"
"\n"
"        許可されていない: ユーザは割当を申請することができません。"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__feb
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__feb
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__feb
msgid "February"
msgstr "2月"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__duration_display
msgid ""
"Field allowing to see the allocation duration in days or hours depending on "
"the type_request_unit"
msgstr "type_request_unitに応じて、割り当て期間を日数または時間で表示できるフィールド"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__duration_display
msgid ""
"Field allowing to see the leave request duration in days or hours depending "
"on the leave_type_request_unit"
msgstr "Leave_type_request_unitに応じて、休暇申請の期間を日数または時間で確認できるフィールド"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__first_approver_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__approver_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "First Approval"
msgstr "最初の承認"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day
msgid "First Day"
msgstr "初日"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day_display
msgid "First Day Display"
msgstr "初日表示"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month
msgid "First Month"
msgstr "初月"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day
msgid "First Month Day"
msgstr "月初日"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day_display
msgid "First Month Day Display"
msgstr "月初日表示"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_follower_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_partner_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesomeのアイコン 例. fa-tasks"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days_display
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid ""
"For an Accrual Allocation, this field contains the theorical amount of time "
"given to the employee, due to a previous start date, on the first run of the"
" plan. This can be manually edited."
msgstr ""
"累積割当の場合、このフィールドには、計画の最初の実行時に、以前の開始日により従業員に与えられた時間の理論的な量が含まれます。これは手動で編集できます。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__frequency
msgid "Frequency"
msgstr "頻度"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__fri
msgid "Friday"
msgstr "金曜"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__start_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "From"
msgstr "from"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_from
msgid "From Date"
msgstr "開始日"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Future Activities"
msgstr "今後の活動"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "Generate Time Off"
msgstr "休暇を生成"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_allocation_generate_multi_wizard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
msgid "Generate time off allocations for multiple employees"
msgstr " 複数の従業員に対する休暇割当の生成"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_generate_multi_wizard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "Generate time off for multiple employees"
msgstr "複数の従業員の休暇を作成"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_leave_allocation_generate_multi_wizard.py:0
msgid "Generated Allocations"
msgstr " 生成済の休暇割当"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_leave_generate_multi_wizard.py:0
msgid "Generated Time Off"
msgstr "生成済の休暇"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "Grant Time"
msgstr "時間調整"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Group By"
msgstr "グループ化"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__group_days_leave
msgid "Group Time Off"
msgstr "グループ休暇"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_approval
msgid "HR Approval"
msgstr "人事承認"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_summary_employee
msgid "HR Time Off Summary Report By Employee"
msgstr "従業員によるHR休暇要約レポート"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_half
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__half_day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__half_day
msgid "Half Day"
msgstr "半日"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr "部門マネジャーアクセス権あり"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__has_mandatory_day
msgid "Has Mandatory Day"
msgstr "強制日あり"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__has_message
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "Has Valid Allocation"
msgstr "有効な割当あり"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_hatched
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_hatched
msgid "Hatched"
msgstr "開始された"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__holiday_status
msgid "Holiday Status"
msgstr "休暇ステータス"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_report_hr_holidays_report_holidayssummary
msgid "Holidays Summary Report"
msgstr "休日概要レポート"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_from
msgid "Hour from"
msgstr "からの時間"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_to
msgid "Hour to"
msgstr "時間から"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__hourly
msgid "Hourly"
msgstr "時間毎"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__added_value_type__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__hour
msgid "Hours"
msgstr "時間"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__hr_icon_display
msgid "Hr Icon Display"
msgstr "時間アイコン表示"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__id
msgid "ID"
msgstr "ID"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon"
msgstr "アイコン"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "例外活動を示すアイコン"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
msgid "Idle"
msgstr "アイドル"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "If checked, new messages require your attention."
msgstr "チェックした場合は、新しいメッセージに注意が必要です。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合は、一部のメッセージに配信エラーが発生されました。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__is_based_on_worked_time
msgid ""
"If checked, the accrual period will be calculated according to the work "
"days, not calendar days."
msgstr "チェックした場合、累積期間は暦日ではなく勤務日数で計算されます。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__allows_negative
msgid ""
"If checked, users request can exceed the allocated days and balance can go "
"in negative."
msgstr "チェックが入っていると、ユーザの申請が割当てられた日数を超えてしまい、残高が負になる可能性があります。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__active_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__active_employee
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr "アクティブ項目をFalse にセットすると、リソースレコードは削除することなく非表示にできます。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__active
msgid ""
"If the active field is set to false, it will allow you to hide the time off "
"type without removing it."
msgstr "アクティブフィールドがfalseに設定されている場合、タイムオフタイプを削除せずに非表示にできます。"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_duration_check
msgid ""
"If you want to change the number of days you should use the 'period' mode"
msgstr "日数を変更したい場合は、'期間'モードを使用する必要があります"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__immediately
msgid "Immediately"
msgstr "即時"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Incorrect state for new allocation"
msgstr "新規割当用の不正なステータス"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_is_follower
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__is_name_custom
msgid "Is Name Custom"
msgstr "カスタム名か"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__is_officer
msgid "Is Officer"
msgstr "役員"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__unpaid
msgid "Is Unpaid"
msgstr "未払いです"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jan
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jan
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jan
msgid "January"
msgstr "1月"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__job_id
msgid "Job"
msgstr "職務"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Job Position"
msgstr "職位"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__jul
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jul
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jul
msgid "July"
msgstr "7月"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jun
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jun
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jun
msgid "June"
msgstr "6月"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid "Keep track of your PTOs."
msgstr "PTOを追跡します。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__time_type
msgid "Kind of Time Off"
msgstr "休暇の種類"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Late Activities"
msgstr "遅れた活動"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__leave_id
msgid "Leave"
msgstr "解除"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_increases_duration
msgid "Leave Type Increases Duration"
msgstr "休暇タイプが期間を延長します"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__left
msgid "Left"
msgstr "左"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
msgid "Legend"
msgstr "凡例"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Let's approve it"
msgstr "承認しましょう"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Let's discover the Time Off application"
msgstr "休暇アプリケーションを試してみましょう。"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Let's go validate it"
msgstr "検証しましょう"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Let's try to create a Sick Time Off, select it in the list"
msgstr "病欠を作成してみましょう、リストから選択します。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_count
msgid "Levels"
msgstr "レベル"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Limit to"
msgstr "上限"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要添付"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_management
msgid "Management"
msgstr "マネジメント"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_manager
msgid "Manager"
msgstr "マネジャー"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_manager_approval
msgid "Manager Approval"
msgstr "マネージャーの承認"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_mandatory_day
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
msgid "Mandatory Day"
msgstr "強制日"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_mandatory_day_action
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_mandatory_day_menu_configuration
msgid "Mandatory Days"
msgstr "強制日"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__mar
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__mar
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__mar
msgid "March"
msgstr "3月"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "Mark as ready to approve"
msgstr "承認準備完了とする"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__max_leaves
msgid "Max Leaves"
msgstr "最大の葉"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Max Time Off:"
msgstr "最大休暇:"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__max_leaves
msgid "Maximum Allowed"
msgstr "最大許容数"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__max_allowed_negative
msgid "Maximum Excess Amount"
msgstr "最大超過額"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid ""
"Maximum Time Off Allowed - Time Off Already Taken - Time Off Waiting "
"Approval"
msgstr "許可される最大休暇-すでに取得された休暇-承認待ちの休暇"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__postpone_max_days
msgid "Maximum amount of accruals to transfer"
msgstr "繰越上限"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__may
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__may
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__may
msgid "May"
msgstr "5月"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid "Meet the time off dashboard."
msgstr "ダッシュボードの休暇をご覧ください。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__meeting_id
msgid "Meeting"
msgstr "ミーティング"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_mail_message_subtype
msgid "Message subtypes"
msgstr "メッセージのサブタイプ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_ids
msgid "Milestone"
msgstr "マイルストン"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid "Milestone Transition"
msgstr "マイルストン移行"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time_yearly
msgid "Milestone cap"
msgstr "マイルストン限度"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "Mode"
msgstr "モード"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__mon
msgid "Monday"
msgstr "月曜"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Month"
msgstr "月"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__monthly
msgid "Monthly"
msgstr "月次"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__accrual_validity_type__month
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__month
msgid "Months"
msgstr "月"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__am
msgid "Morning"
msgstr "午前"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/components/multi_time_off_generation_menu/multiple_time_off_generation_menu.xml:0
#: model:ir.actions.act_window,name:hr_holidays.action_hr_leave_allocation_generate_multi_wizard
#: model:ir.actions.act_window,name:hr_holidays.action_hr_leave_generate_multi_wizard
msgid "Multiple Requests"
msgstr "複数申請"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "活動期限"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_my
#: model:ir.ui.menu,name:hr_holidays.menu_open_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Allocations"
msgstr "自分の割当申請"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Department"
msgstr "自分の部門"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "My Requests"
msgstr "自分の依頼"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Team"
msgstr "自分のチーム"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_my_leaves
msgid "My Time"
msgstr "自分の休暇"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_my
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "My Time Off"
msgstr "自分の休暇"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_leaves_tree_inherit
msgid "Name"
msgstr "名称"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Negative Cap"
msgstr "負の限度"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.xml:0
msgid "New"
msgstr "新規"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "New %(leave_type)s Request created by %(user)s"
msgstr "新規 %(leave_type)s 申請が %(user)sによって作成されました"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/hooks.js:0
msgid "New Allocation"
msgstr "新しい割り当て"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "New Allocation Request"
msgstr "新規割当申請"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"New Allocation Request created by %(user)s: %(count)s Days of "
"%(allocation_type)s"
msgstr "%(user)sによって作成された新しい割当申請：%(count)s%(allocation_type)sの日数"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "New Milestone"
msgstr "新規マイルストン"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
msgid "New Time Off"
msgstr "新しい休暇"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "次の活動カレンダーイベント"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "次の活動期限"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_summary
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_summary
msgid "Next Activity Summary"
msgstr "次の活動概要"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_id
msgid "Next Activity Type"
msgstr "次の活動タイプ"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__no
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No Limit"
msgstr "制限なし"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__no_validation
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__no_validation
msgid "No Validation"
msgstr "承認不要"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.action_hr_available_holidays_report
#: model_terms:ir.actions.act_window,help:hr_holidays.action_hr_leave_report
#: model_terms:ir.actions.act_window,help:hr_holidays.mail_activity_type_action_config_hr_holidays
msgid "No data to display"
msgstr "表示するデータがありません"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_report_action
msgid "No data yet!"
msgstr "まだデータはありません！"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No limit"
msgstr "制限なし"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "No rule has been set up for this accrual plan."
msgstr "この累計休暇計画用に設定された規則はありません"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Nobody will be notified"
msgstr "通知を受ける人はいません"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__show_on_dashboard
msgid ""
"Non-visible allocations can still be selected when taking a leave, but will "
"simply not be displayed on the leave dashboard."
msgstr "表示されない割当は、休暇取得時に選択できますが、休暇ダッシュボードには表示されません。"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "None"
msgstr "なし"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__lost
msgid "None. Accrued time reset to 0"
msgstr "なし。累積時間を0に再設定"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__no
msgid "Not Allowed"
msgstr "不可"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__responsible_ids
msgid "Notified Time Off Officer"
msgstr "通知先休暇担当者"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__nov
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__nov
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__nov
msgid "November"
msgstr "11月"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of Actions"
msgstr "アクション数"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_list
msgid "Number of Days"
msgstr "日数"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__number_of_hours
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__number_of_hours
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Number of Hours"
msgstr "時間"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leaves_count
msgid "Number of Time Off"
msgstr "休暇の数"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_days
msgid "Number of days of the time off request. Used in the calculation."
msgstr "休暇申請日数。計算に使用されます。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_hours
msgid "Number of hours of the time off request. Used in the calculation."
msgstr "休暇申請時間。計算に使用されます。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "アクションを必要とするメッセージの数"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーが発生されたメッセージ数"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__oct
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__oct
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__oct
msgid "October"
msgstr "10月"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Off Today"
msgstr "本日の休暇"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_user
msgid "Officer: Manage all requests"
msgstr "役員: 全申請の管理"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "On Time Off"
msgstr "休暇中"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_absent
msgid "On leave"
msgstr "休暇中"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
msgid "Online"
msgstr "オンライン"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Only"
msgstr "只今"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"Only %s's Time Off Approver, a time off Officer/Responsible or Administrator"
" can approve or refuse allocation requests."
msgstr "%s の休暇承認者、休暇担当/責任者、または管理者のみが、休暇割当要求を承認または拒否できます。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Only a Time Off Manager can reset a refused leave."
msgstr "否認された休暇をリセットできるのは、休暇管理者のみです。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Only a Time Off Manager can reset a started leave."
msgstr "開始した休暇をリセットできるのは、休暇管理者のみです。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Only a Time Off Manager can reset other people leaves."
msgstr "他の従業員の休暇をリセットできるのは、休暇管理者のみです。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"Only a Time Off Officer or Manager can approve/refuse its own requests."
msgstr "休暇管理者またはマネジャーのみが、自身の申請を承認/否認できます。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Only a manager can modify a canceled leave."
msgstr "取消済休暇を変更できるのはマネジャーのみです。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Only a time off Administrator can approve their own requests."
msgstr "休暇管理者だけが、自分の申請を承認できます。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"Only a time off Officer/Responsible or Administrator can approve or refuse "
"allocation requests."
msgstr "休暇担当/管理者、または管理者のみが、休暇割当申請を承認または否認することができます。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee_base.py:0
msgid "Operation not supported"
msgstr "操作はサポートされていません"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__other
msgid "Other"
msgstr "その他"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
msgid "Out of office"
msgstr "オフィスの外"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_dashboard
msgid "Overview"
msgstr "概要"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_cl
msgid "Paid Time Off"
msgstr "有給休暇"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.hr_holiday_status_dv
msgid "Parental Leaves"
msgstr "育児休暇"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "Pending Requests"
msgstr "保留中申請"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Period"
msgstr "期間"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__planned
msgid "Planned"
msgstr "予定済"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Planned:"
msgstr "計画済:"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_present
msgid "Present but on leave"
msgstr "存在するが休暇中"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Print"
msgstr "印刷"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
msgid "Provide a reason to cancel an approved time off"
msgstr "承認済休暇の取消理由の提示"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_form_inherit
msgid "Public"
msgstr "パブリック"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__include_public_holidays_in_duration
msgid "Public Holiday Included"
msgstr " 祝日を含む"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.actions.act_window,name:hr_holidays.open_view_public_holiday
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_public_time_off_menu_configuration
msgid "Public Holidays"
msgstr "祝祭日"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__include_public_holidays_in_duration
msgid ""
"Public holidays should be counted in the leave duration when applying for "
"leaves"
msgstr "休暇を申請する際、祝日は休暇期間に算入する必要があります。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value
msgid "Rate"
msgstr "レート"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__rating_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__rating_ids
msgid "Ratings"
msgstr "評価"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__reason
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Reason"
msgstr "理由"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__notes
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__notes
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__notes
msgid "Reasons"
msgstr "理由"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/views/calendar/common/calendar_common_popover.xml:0
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Refuse"
msgstr "否認"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__refuse
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_dashboard_new_time_off
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Refused"
msgstr "否認"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Refused Time Off"
msgstr "否認された休暇"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__regular
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_type__regular
msgid "Regular Allocation"
msgstr "通常割当"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Remaining Days"
msgstr "残り日数"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Remaining Hours"
msgstr "残時間"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Remaining leaves"
msgstr "休暇の残り"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_report
msgid "Reporting"
msgstr "レポーティング"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Allocation"
msgstr "割当申請"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_to
msgid "Request End Date"
msgstr "申請終了日"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from
msgid "Request Start Date"
msgstr "申請開始日"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Time off"
msgstr "休暇申請"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__leave_type
msgid "Request Type"
msgstr "申請タイプ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__duration_display
msgid "Requested (Days/Hours)"
msgstr "申請（日数/時間数）"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__requires_allocation
msgid "Requires allocation"
msgstr "割当申請"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Reset"
msgstr "リセット"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__resource_calendar_id
msgid "Resource Calendar"
msgstr "リソースカレンダ"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.resource_calendar_global_leaves_action_from_calendar
msgid "Resource Time Off"
msgstr "休暇"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "リソース休暇の詳細"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar
msgid "Resource Working Time"
msgstr "リソース作業時間"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_user_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_user_id
msgid "Responsible User"
msgstr "担当ユーザ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Rules"
msgstr "規則"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Run until"
msgstr "有効期限"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS配信エラー"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sat
msgid "Saturday"
msgstr "土曜"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
msgid "Save"
msgstr "保存"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Search Time Off"
msgstr "検索休暇"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Search Time Off Type"
msgstr "検索時間オフタイプ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Search allocations"
msgstr "検索の割り当て"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__second_approver_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__second_approver_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate1
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Second Approval"
msgstr "第2の承認"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day
msgid "Second Day"
msgstr "2日目"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day_display
msgid "Second Day Display"
msgstr "2日目表示"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month
msgid "Second Month"
msgstr "2月目"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day
msgid "Second Month Day"
msgstr "月2日目"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day_display
msgid "Second Month Day Display"
msgstr "月2日目表示"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Second approval request for %(allocation_type)s"
msgstr " %(allocation_type)s用の2次承認依頼"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Second approval request for %(leave_type)s"
msgstr "%(leave_type)s用の第2承認の要求"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Select Time Off"
msgstr "休暇を選択"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__holiday_type
msgid "Select Time Off Type"
msgstr "休暇タイプの選択"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__validation_type
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__allocation_validation_type
msgid ""
"Select the level of approval needed in case of request by employee\n"
"            #     - No validation needed: The employee's request is automatically approved.\n"
"            #     - Approved by Time Off Officer: The employee's request need to be manually approved\n"
"            #       by the Time Off Officer, Employee's Approver or both."
msgstr ""
"従業員からの申請の場合に必要な承認レベルを選択します。\n"
"            #     - 検証の必要なし: 従業員の申請は自動的に承認されます。\n"
"            #     - 休暇担当による承認: 従業員の申請を手動で承認する必要があります。\n"
"            #      休暇担当、従業員の承認者、またはその両方によるもの。"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Select the request you just created"
msgstr "作成したばかりの申請を選択"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_report_calendar__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_res_users__leave_manager_id
msgid ""
"Select the user responsible for approving \"Time Off\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr "この従業員の'休暇'の承認を担当するユーザーを選択します。空の場合、承認は管理者または承認者(設定/ユーザーで決定)によって行われます。"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__sep
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__sep
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__sep
msgid "September"
msgstr "9月"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__sequence
msgid "Sequence"
msgstr "シーケンス"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "Sequence is generated automatically by start time delta."
msgstr "順序は開始時間の差分によって自動的に生成されます。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__postpone_max_days
msgid "Set a maximum of accruals an allocation keeps at the end of the year."
msgstr "割当で年末に保持される累積休暇の上限を設定します。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__show_on_dashboard
msgid "Show On Dashboard"
msgstr "ダッシュボードに表示"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__show_transition_mode
msgid "Show Transition Mode"
msgstr "移行モードを表示"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Show all records which has next action date is before today"
msgstr "次のアクションの日付が今日より前のすべてのレコードを表示"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_sl
#: model:mail.message.subtype,description:hr_holidays.mt_leave_sick
#: model:mail.message.subtype,name:hr_holidays.mt_leave_sick
msgid "Sick Time Off"
msgstr "病気休暇"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Some leaves cannot be linked to any allocation. To see those leaves,"
msgstr "一部の休暇は、どの割当にもリンクできません。それらの休暇を見るには"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
msgid ""
"Some of the accrual plans you're trying to delete are linked to an existing "
"allocation. Delete or cancel them first."
msgstr "削除しようとしている休暇累積プランの中には、既存の割当にリンクされているものがあります。まず、それらを削除または取消して下さい。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
msgid ""
"Specify if this accrual plan can only be used with this Time Off Type.\n"
"                Leave empty if this accrual plan can be used with any Time Off Type."
msgstr ""
"この累積プランがこの休暇タイプでのみ使用できる場合は指定します。\n"
"この累積プランがどの休暇タイプでも使用できる場合は、空のままにします。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid ""
"Specify what occurs if a level transition takes place in the middle of a pay period.\n"
"\n"
"                'Immediately' will switch the employee to the new accrual level on the exact date during the ongoing pay period.\n"
"\n"
"                'After this accrual's period' will keep the employee on the same accrual level until the ongoing pay period is complete.\n"
"                After it is complete, the new level will take effect when the next pay period begins."
msgstr ""
"給与期間の途中でレベル移行が行われた場合の処理を指定します。\n"
"\n"
"'即時'は、継続中の給与期間中の正確な日付に、従業員を新しい累積休暇レベルに切り替えます。\n"
"\n"
"'この累積休暇期間の終了後'は、継続中の給与期間が終了するまで従業員を同じ累積レベルに維持します。\n"
"期間終了後、次の給与期間の開始時に新しいレベルが有効になります。"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Start Accruing"
msgstr "繰越開始"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__start_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_from
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Start Date"
msgstr "開始日"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__start_count
msgid "Start after"
msgstr "開始"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__state
msgid "State"
msgstr "都道府県・州"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__state
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Status"
msgstr "状態"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"活動に基づくステータス\n"
"遅延: 期限が既に過ぎています\n"
"今日: 活動日は今日です\n"
"予定: 将来の活動。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_striked
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_striked
msgid "Striked"
msgstr "ストライキ"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Submit your request"
msgstr "申請を提出"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Sum"
msgstr "合計"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sun
msgid "Sunday"
msgstr "日曜"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids_count
msgid "Supported Attachment Ids Count"
msgstr "サポート添付ID数"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_support_document
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__support_document
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Supporting Document"
msgstr "参考資料"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Supporting Documents"
msgstr "参考資料"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__request_unit
msgid "Take Time Off in"
msgstr "休暇時間単位"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__taken
msgid "Taken"
msgstr "取得済"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISOの国別コードは2文字です。\n"
"これを使ってクイックサーチできます。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"The Start Date of the Validity Period must be anterior to the End Date."
msgstr "有効期間の開始日は終了日より前である必要があります。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_count
msgid ""
"The accrual starts after a defined period from the allocation start date. "
"This field defines the number of days, months or years after which accrual "
"is used."
msgstr "累積は、割当開始日から定義された期間後に開始します。このフィールドは、累積が使用される日数、月数、または年数を定義します。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid ""
"The allocation requirement of a time off type cannot be changed once leaves "
"of that type have been taken. You should create a new time off type instead."
msgstr "休暇タイプの割当要件は、そのタイプの休暇を取得した後は変更できません。代わりに新しい休暇タイプを作成して下さい。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__color
msgid ""
"The color selected here will be used in every screen with the time off type."
msgstr "ここで選択された色は、休暇タイプの全ての画面で使用されます。"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_check_dates
msgid "The dates you've set up aren't correct. Please check them."
msgstr "設定した日付が正しくありません。確認して下さい。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__time_type
msgid ""
"The distinction between working time (ex. Attendance) and absence (ex. "
"Training) will be used in the computation of Accrual's plan rate."
msgstr "勤務時間(例: 出勤)と欠勤(例: 研修)の区別は、累積プラン率の計算に使用されます。"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_allocation_duration_check
msgid "The duration must be greater than 0."
msgstr "期間は0より大きくなければなりません。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_departure_wizard.py:0
msgid "The employee no longer works in the company"
msgstr "従業員は会社を退職しました"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"The following employees are not supposed to work during that period:\n"
" %s"
msgstr "次の従業員は、その期間中は働くことになっていない:%s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid ""
"The leaves planned in the future are exceeding the maximum value of the allocation.\n"
"                It will not be possible to take all of them."
msgstr ""
"今後予定されている休暇は、割当ての最大値を超えています。\n"
"全て取得することは不可能です。"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_type_check_negative
msgid ""
"The maximum excess amount should be greater than 0. If you want to set 0, "
"disable the negative cap instead."
msgstr "最大超過額は0より大きい値にする必要があります。0を設定したい場合は、代わりに負の限度を無効にして下さい。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__expiring_carryover_days
msgid ""
"The number of carried over days that will expire on "
"carried_over_days_expiration_date"
msgstr "carried_over_days_expiration_date に有効期限が切れる繰越日数"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__added_value
msgid ""
"The number of hours/days that will be incremented in the specified Time Off "
"Type for every period"
msgstr "指定された休暇タイプで期間ごとに増分される時間/日数"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_date_check3
msgid ""
"The request start date must be before or equal to the request end date."
msgstr "申請開始日は申請終了日より前でなければなりません。"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_mandatory_day_date_from_after_day_to
msgid "The start date must be anterior than the end date."
msgstr "開始日は終了日より前にしてください。"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_date_check2
msgid "The start date must be before or equal to the end date."
msgstr "開始日は終了日以前でなければなりません。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__state
msgid ""
"The status is 'To Approve', when an allocation request is created.\n"
"The status is 'Refused', when an allocation request is refused by manager.\n"
"The status is 'Approved', when an allocation request is approved by manager."
msgstr ""
"休暇申請が作成されると、ステータスは'承認待ち'となります。\n"
"マネジャーにより休暇申請が却下された場合、ステータスは'否認'となります。\n"
"休暇申請がマネジャーに承認されると、ステータスは'承認済'となります。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__state
msgid ""
"The status is set to 'To Submit', when a time off request is created.\n"
"The status is 'To Approve', when time off request is confirmed by user.\n"
"The status is 'Refused', when time off request is refused by manager.\n"
"The status is 'Approved', when time off request is approved by manager."
msgstr ""
"休暇申請が作成されると、ステータスは'送信予定'に設定されます。ユーザが休暇申請を確定すると、ステータスは'承認中'になります。マネージャーが休暇申請を否認した場合、ステータスは'否認'です。休暇申請がマネージャーによって承認されると、ステータスは'承認済'になります。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "The time off has been automatically approved"
msgstr "休暇は自動的に承認されました"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "The time off has been cancelled: %s"
msgstr "休暇が取消されました: %s"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__sequence
msgid ""
"The type with the smallest sequence is the default value in time off request"
msgstr "シーケンスが最小のタイプは、休暇申請のデフォルト値です。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"There is no employee set on the time off. Please make sure you're logged in "
"the correct company."
msgstr "休暇に従業員が設定されていません。正しい会社にログインしていることを確認して下さい。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "There is no valid allocation to cover that request."
msgstr "その申請をカバーできる有効な割当がありません。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"This allocation have already ran once, any modification won't be effective "
"to the days allocated to the employee. If you need to change the "
"configuration of the allocation, delete and create a new one."
msgstr ""
"この割当は一度実行されているため、どのような変更を加えても、その従業員に割当てられた日数には反映されません。割当の設定を変更する必要がある場合は、取消して新しい割当を作成して下さい。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__first_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off"
msgstr "この領域は、休暇を検証するユーザーによって自動的に入力されます"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__second_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off with"
" second level (If time off type need second validation)"
msgstr "この領域は、休暇を2番目のレベルで検証するユーザーによって自動的に入力されます(休暇タイプに2番目の検証が必要な場合)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__approver_id
msgid ""
"This area is automatically filled by the user who validates the allocation"
msgstr "この領域は、割り当てを検証するユーザーによって自動的に入力されます"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__second_approver_id
msgid ""
"This area is automatically filled by the user who validates the allocation "
"with second level (If time off type need second validation)"
msgstr "このエリアは、2次レベルで休暇割当を検証するユーザによって自動的に入力されます(休暇タイプが2次承認を必要とする場合)。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__accrual_validity_type
msgid "This field defines the unit of time after which the accrual ends."
msgstr "このフィールドは、繰越休暇が終了するまでの期間を定義します。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_type
msgid "This field defines the unit of time after which the accrual starts."
msgstr "このフィールドは、累積を開始する時間の単位を定義します。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "This indicates if it is still possible to use this type of leave"
msgstr "これは、このタイプの休暇をまだ使用できるかどうかを示します"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "This modification is not allowed in the current state."
msgstr "現在の状態では、この変更は許可されていません。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "This time off cannot be cancelled."
msgstr "この休暇は取消できません。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__leaves_taken
msgid ""
"This value is given by the sum of all time off requests with a negative "
"value."
msgstr "この値は、負の値を持つすべてのタイムオフ要求の合計によって与えられます。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__max_leaves
msgid ""
"This value is given by the sum of all time off requests with a positive "
"value."
msgstr "この値は、正の値を持つすべてのタイムオフ要求の合計によって与えられます。"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__thu
msgid "Thursday"
msgstr "木曜"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_holiday_allocation_id
#: model:ir.model,name:hr_holidays.model_hr_leave
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_manager_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__request
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_root
#: model:ir.ui.menu,name:hr_holidays.menu_open_department_leave_approve
#: model:mail.message.subtype,name:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
msgid "Time Off"
msgstr "休暇"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr "休暇割当"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/hr_leave_employee_type_report.py:0
#: model:ir.actions.act_window,name:hr_holidays.action_hr_available_holidays_report
#: model:ir.actions.act_window,name:hr_holidays.action_hr_leave_report
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_report_action
msgid "Time Off Analysis"
msgstr "休暇分析"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_approval
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
msgid "Time Off Approval"
msgstr "休暇の承認"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_tree_inherit_leave
msgid "Time Off Approver"
msgstr "休暇承認者"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_report_calendar
msgid "Time Off Calendar"
msgstr "休暇カレンダー"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar__associated_leaves_count
msgid "Time Off Count"
msgstr "休暇数"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee.py:0
msgid "Time Off Dashboard"
msgstr "休暇ダッシュボード"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__private_name
msgid "Time Off Description"
msgstr "休暇の説明"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_notif_subtype_id
msgid "Time Off Notification Subtype"
msgstr "休暇通知サブタイプ"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid ""
"Time Off Officers allocate time off days to employees (e.g. paid time off).<br>\n"
"                Employees request allocations to Time Off Officers (e.g. recuperation days)."
msgstr ""
"休暇担当者は、従業員に休暇日を割り当てます(例:有給休暇)。<br>\n"
"従業員は、休暇担当者へ割り当てを要求します(例:休暇日)。"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my_request
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__leave_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__leave_id
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar_leaves__holiday_id
#: model:mail.message.subtype,description:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_employee_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Time Off Request"
msgstr "休暇申請"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Time Off Requests"
msgstr "休暇申請"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_responsible
msgid "Time Off Responsible"
msgstr "休暇責任者"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_second_approval
msgid "Time Off Second Approve"
msgstr "休暇の2番目の承認"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_summary_employee
#: model:ir.actions.report,name:hr_holidays.action_report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_list
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_pivot
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Time Off Summary"
msgstr "休暇の概要"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_employee_type_report
#: model:ir.model,name:hr_holidays.model_hr_leave_report
msgid "Time Off Summary / Report"
msgstr "休暇の概要/レポート"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Time Off Taken:"
msgstr "休暇を取る:"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off Type"
msgstr "休暇タイプ"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_holiday_status
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_status_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Time Off Types"
msgstr "休暇タイプ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_validation_type
msgid "Time Off Validation"
msgstr "休暇の検証"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off of Your Team Member"
msgstr "チームメンバーの休暇"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__leave_to_approve_count
msgid "Time Off to Approve"
msgstr "承認待ちの休暇"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Time Off."
msgstr "休暇."

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.hr_leave_cron_cancel_invalid_ir_actions_server
msgid "Time Off: Cancel invalid leaves"
msgstr "休暇: 無効な休暇を取消"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Time off"
msgstr "休暇"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leaves_taken
msgid "Time off Already Taken"
msgstr "休暇はすでに取られています"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_holidays_by_employee_and_type_report
msgid "Time off Analysis by Employee and Time Off Type"
msgstr "従業員ごとの休暇分析と休暇タイプ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_pivot
msgid "Time off Summary"
msgstr "休暇の概要"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__leaves_taken
msgid "Time off Taken"
msgstr "取得済休暇"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time off of people you are manager of"
msgstr "あなたがマネージャーである人々の休暇"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"Time off request must be confirmed (\"To Approve\") in order to approve it."
msgstr "休暇申請を承認するには、休暇申請が確認済 (\"承認待ち\") である必要があります。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Time off request must be confirmed in order to approve it."
msgstr "休暇申請を承認するには、休暇申請が確認済である必要があります。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Time off request must be confirmed or validated in order to refuse it."
msgstr "休暇申請を否認するには、休暇申請を確認または検証する必要があります。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"Time off request state must be \"Refused\" or \"Cancelled\" in order to be "
"reset to \"Confirmed\"."
msgstr "休暇申請ステータスを\"確認済\"にするには、\"否認済\" または \"取消済\" である必要があります。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__tz
msgid "Timezone"
msgstr "時間帯"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Title"
msgstr "タイトル"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__stop_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "To"
msgstr "終了日"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__confirm
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "To Approve"
msgstr "承認待ち"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "To Approve or Approved Allocations"
msgstr "承認待ちまたは承認済の割当"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_date_to
msgid "To Date"
msgstr "終了日"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "Today"
msgstr "今日"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Today Activities"
msgstr "本日の活動"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocations_count
msgid "Total number of allocations"
msgstr "割当合計数"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_count
msgid "Total number of days allocated."
msgstr "割り当てられた合計日数。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__remaining_leaves
msgid ""
"Total number of paid time off allocated to this employee, change this value "
"to create allocation/time off request. Total based on all the time off types"
" without overriding limit."
msgstr ""
"この従業員に割当てられている有給休暇の合計数。この値を変更すると、割当/休暇申請が作成されます。合計は、制限を超えることなく、全ての休暇タイプに基づいています。"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_training
msgid "Training Time Off"
msgstr "研修休暇"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.mail_activity_type_action_config_hr_holidays
msgid ""
"Try to add some records, or make sure that there is no active filter in the "
"search bar."
msgstr "レコードを追加するか、検索バーに有効なフィルタが設定されていないことを確認して下さい。"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__tue
msgid "Tuesday"
msgstr "火曜"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__bimonthly
msgid "Twice a month"
msgstr "月に２回"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__biyearly
msgid "Twice a year"
msgstr "年に２回"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
msgid ""
"Two public holidays cannot overlap each other for the same working hours."
msgstr "2つの祝祭日が同じ労働時間で重なることはありません。"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Type"
msgstr "タイプ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__type_request_unit
msgid "Type Request Unit"
msgstr "タイプ申請単位"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記録上の例外活動の種類。"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz
msgid "Tz"
msgstr "Tz"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz_mismatch
msgid "Tz Mismatch"
msgstr "Tzミスマッチ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Unlimited"
msgstr "無制限"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_unpaid
msgid "Unpaid"
msgstr "無給休暇"

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_leave_unpaid
#: model:mail.message.subtype,name:hr_holidays.mt_leave_unpaid
msgid "Unpaid Time Off"
msgstr "無給休暇"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Unread Messages"
msgstr "未読メッセージ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Up to"
msgstr "以下まで："

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_users
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__user_id
msgid "User"
msgstr "ユーザ"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
msgid "User is idle"
msgstr "ユーザーはアイドル状態です"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
msgid "User is online"
msgstr "ユーザーはオンラインです"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
msgid "User is out of office"
msgstr "ユーザーが不在です"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/static/src/views/calendar/common/calendar_common_popover.xml:0
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Validate"
msgstr "検証"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
msgid "Validated"
msgstr "検証済"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__validation_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__validation_type
msgid "Validation Type"
msgstr "検証タイプ"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_departure_wizard.py:0
msgid ""
"Validity End date has been updated because Employee no longer works in the "
"company"
msgstr "従業員の勤務が終了したため、有効期限を更新しました。"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "Validity Period"
msgstr "有効期間"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Start"
msgstr "検証開始"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Stop"
msgstr "検証中断"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid "Virtual Remaining Time Off"
msgstr "仮想残り時間オフ"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__confirm
msgid "Waiting Approval"
msgstr "承認待ち"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Waiting For Me"
msgstr "自分の承認待ち"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate1
msgid "Waiting Second Approval"
msgstr "2番目の承認待ち"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Waiting for Approval"
msgstr "承認待ち"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__website_message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__website_message_ids
msgid "Website Messages"
msgstr "ウェブサイトメッセージ"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__website_message_ids
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__website_message_ids
msgid "Website communication history"
msgstr "ウェブサイト通信履歴"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__wed
msgid "Wednesday"
msgstr "水曜"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__weekly
msgid "Weekly"
msgstr "週次"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__action_with_unused_accruals
msgid ""
"When the Carry-Over Time is reached, according to Plan's setting, select "
"what you want to happen with the unused time off: None (time will be reset "
"to zero), All accrued time carried over to the next period; or Carryover "
"with a maximum)."
msgstr ""
"プランの設定に従って、繰越時間に達した場合、未使用の休暇をどうするかを選択します:なし(時間はゼロにリセットされます)、発生した時間は全て次の期間に繰越されます;または繰越しに上限を設定します)。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time
msgid ""
"When the field is checked the balance of an allocation using this accrual "
"plan will never exceed the specified amount."
msgstr "休暇累積プランを使用して、このフィールドがチェックされている場合、割当の残日数が指定された日数を超えることはありません。"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time_yearly
msgid ""
"When the field is checked the total amount accrued each year will be capped "
"at the specified amount"
msgstr "このフィールドを選択すると、年間累計休暇数合計が指定された制限に制限されます。"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__other
msgid "Worked Time"
msgstr "就業時間"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__resource_calendar_id
msgid "Working Hours"
msgstr "労働時間"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__yearly
msgid "Yearly"
msgstr "年次"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day
msgid "Yearly Day"
msgstr "年間日"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day_display
msgid "Yearly Day Display"
msgstr "年間日の表示"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_month
msgid "Yearly Month"
msgstr "年間月"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__maximum_leave_yearly
msgid "Yearly limit to"
msgstr "年次制限:"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__year
msgid "Years"
msgstr "年"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__yes
msgid "Yes"
msgstr "はい"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__requires_allocation
msgid ""
"Yes: Time off requests need to have a valid allocation.\n"
"\n"
"              No Limit: Time Off requests can be taken without any prior allocation."
msgstr ""
"はい: 休暇は有効な割当が必要です。\n"
"\n"
"              制限なし: 休暇の申請は、事前の割当なしに取得することができます。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "You are not allowed to request time off on a Mandatory Day"
msgstr "強制日に休暇申請することはできません"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__accrual_validity_count
msgid ""
"You can define a period of time where the days carried over will be "
"available"
msgstr "繰越日数の利用可能期間を定義することができます。"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_start_count_check
msgid "You can not start an accrual in the past."
msgstr "過去に遡って累積を開始することはできません。"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid ""
"You can select the period you need to take off, from start date to end date"
msgstr "開始日から終了日まで、休暇を取る期間を選択できます。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "You cannot delete a time off which is in %s state"
msgstr "%s状態の休暇は削除できません"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "You cannot delete a time off which is in the past"
msgstr "過去の休暇を削除することはできません。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"You cannot delete an allocation request which has some validated leaves."
msgstr "検証済の休暇のある割当申請を削除することはできません。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "You cannot delete an allocation request which is in %s state."
msgstr "%s状態の割当申請は削除できません。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You cannot first approve a time off for %s, because you are not his time off"
" manager"
msgstr "あなたは彼の休暇マネージャーではないため、最初に%sの休暇を承認することはできません"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan_level.py:0
msgid ""
"You cannot have a cap on accrued time without setting a maximum amount."
msgstr "未払時間に上限を設けずに、上限額を設定することはできません。"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_valid_yearly_cap_value
msgid ""
"You cannot have a cap on yearly accrued time without setting a maximum "
"amount."
msgstr "上限数を設定しない限り、年間累計数に上限を設けることはできません。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid ""
"You cannot modify the 'Public Holiday Included' setting since one or more "
"leaves for that                         time off type are overlapping with "
"public holidays, meaning that the balance of those employees would be "
"affected by this change."
msgstr ""
" "
"'祝日を含む'設定を変更することはできません。その休暇タイプの1つまたは複数の休暇が祝日と重なっているため、この変更によってそれらの従業員のバランスが影響を受けることになるからです。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"You cannot reduce the duration below the duration of leaves already taken by"
" the employee."
msgstr "従業員が既に取得した休暇の期間より短くすることはできません。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"You cannot refuse this allocation request since the employee has already "
"taken leaves for it. Please refuse or delete those leaves first."
msgstr "従業員がすでに休暇を取得しているため、この割当申請を否認することはできません。まず、これらの休暇を否認または削除して下さい。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You don't have the rights to apply second approval on a time off request"
msgstr "休暇申請時に2回目の承認を申請する権利がありません"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "You must be %s's Manager to approve this leave"
msgstr "この休暇の承認には%sのマネジャーである必要があります。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"You must be either %s's Time Off Approver or Time off Administrator to "
"validate this allocation request."
msgstr "この休暇割当申請を検証するには、%s の休暇承認者または休暇管理者のいずれかである必要があります。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You must be either %s's manager or Time off Manager to approve this leave"
msgstr "この休暇を承認するには、%sのマネジャーまたは休暇マネジャーのいずれかである必要があります"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You must either be a Time off Officer or Time off Manager to approve this "
"leave"
msgstr "この休暇を承認するには、休暇担当者または休暇マネジャーのいずれかである必要があります。"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_added_value_greater_than_zero
msgid "You must give a rate greater than 0 in accrual plan levels."
msgstr "累積プランのレベルでは、0より大きい率を与えて下さい。"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You must have manager rights to modify/validate a time off that already "
"begun"
msgstr "すでに開始されている休暇を変更/検証するには、マネージャーの権限が必要です"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You've already booked time off which overlaps with this period:\n"
"%s\n"
"Attempting to double-book your time off won't magically make your vacation 2x better!\n"
msgstr ""
"既にこの期間と重複した休暇を予約済です:%s\n"
"\n"
"2重になっている内容をご確認下さい！\n"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Your %(leave_type)s planned on %(date)s has been accepted"
msgstr "%(date)sに計画された%(leave_type)sが受け入れられました"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Your %(leave_type)s planned on %(date)s has been refused"
msgstr "%(date)s に計画されている %(leave_type)s は否認されました"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Your Time Off"
msgstr "あなたの休暇"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_holidays_cancel_leave.py:0
msgid "Your time off has been cancelled."
msgstr "あなたの休暇が取消されました。"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "after"
msgstr "後"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "after allocation start date"
msgstr "割当開始日後"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "all"
msgstr "全て"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "and"
msgstr "と"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "and on the"
msgstr "と"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "available"
msgstr "取得可能"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_available_holidays_report_tree
msgid "by Employee"
msgstr "従業員別"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_summary_all
msgid "by Type"
msgstr "タイプ別"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "can be used before the allocation expires."
msgstr "割当の有効期限が切れる前に使用可能。"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "click here"
msgstr "ここをクリック"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "day of the month"
msgstr "月の日"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "day(s)"
msgstr "日"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "days"
msgstr "日"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "days of the months"
msgstr "月の日"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "days)"
msgstr "日)"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "e.g. Extra recuperation, Company unavailability, ..."
msgstr "例: 追加療養、会社休業 ..."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "e.g. Time Off type (From validity start to validity end / no limit)"
msgstr "例: 休暇タイプ(有効開始日から有効終了日 / 無制限)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "from %(date_from)s to %(date_to)s - %(state)s"
msgstr "%(date_from)s から %(date_to)s まで- %(state)s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "hour(s)"
msgstr "時間"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "hours"
msgstr "時間"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "in"
msgstr ":"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "initially"
msgstr "主に"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan_level.py:0
msgid "last day"
msgstr "末日"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "new request"
msgstr "新規申請"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "no"
msgstr "no"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "of"
msgstr "の"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "of the"
msgstr "の"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "of the month"
msgstr "月の"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on"
msgstr "on"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on the"
msgstr "以下の:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "refused"
msgstr "否認されました"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "sequence"
msgstr "シーケンス"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "taken"
msgstr "撮影"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "the accruated amount is insufficient for that duration."
msgstr "累積数がその期間用には不足しています。"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "to"
msgstr "to"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "to refuse"
msgstr "未却下"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "up to"
msgstr "以下まで:"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "valid until"
msgstr "有効期限"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "validate"
msgstr "検証"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "validated"
msgstr "検証済"
