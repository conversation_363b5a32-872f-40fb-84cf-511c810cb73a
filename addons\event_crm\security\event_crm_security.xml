<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record id="ir_rule_event_crm" model="ir.rule">
            <field name="name">Event CRM: Multi Company</field>
            <field name="model_id" ref="model_event_lead_rule"/>
            <field name="groups" eval="[(4, ref('base.group_multi_company'))]"/>
            <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
        </record>
    </data>
</odoo>
