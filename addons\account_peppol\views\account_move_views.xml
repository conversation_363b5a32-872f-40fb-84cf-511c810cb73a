<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_peppol_view_move_form" model="ir.ui.view">
        <field name="name">account.peppol.view.move.form</field>
        <field name="model">account.move</field>
        <field name="priority">30</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <header position="inside">
                <button name="action_cancel_peppol_documents"
                        type="object"
                        class="btn btn-secondary"
                        string="Cancel PEPPOL"
                        invisible="peppol_move_state != 'to_send' or state == 'draft'"/>
            </header>

            <xpath expr="//div[@name='journal_div']" position="after">
                <label for="peppol_move_state"
                       invisible="not peppol_move_state or state == 'draft' or move_type in ('in_invoice', 'in_refund')"/>
                <div name="peppol_div"
                     class="d-flex"
                     invisible="not peppol_move_state or state == 'draft' or move_type in ('in_invoice', 'in_refund')">
                    <field name="peppol_move_state" class="oe_inline"/>
                    <span class="mx-1" invisible="'demo_' not in peppol_message_uuid"> (Demo)</span>
                    <span class="text-muted mx-3"
                          invisible="peppol_move_state != 'to_send'">
                        The invoice will be sent automatically via Peppol
                    </span>
                </div>
            </xpath>
        </field>
    </record>

    <record id="account_peppol_view_out_invoice_tree_inherit" model="ir.ui.view">
        <field name="name">account.move.out.invoice.list.inherit</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_out_invoice_tree"/>
        <field name="arch" type="xml">
            <field name="status_in_payment" position="before">
                <field name="peppol_move_state" optional="hide"/>
            </field>
        </field>
    </record>

    <record id="account_peppol_view_out_credit_note_tree_inherit" model="ir.ui.view">
        <field name="name">account.move.credit.note.list.inherit</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_out_credit_note_tree"/>
        <field name="arch" type="xml">
            <field name="status_in_payment" position="before">
                <field name="peppol_move_state" optional="hide"/>
            </field>
        </field>
    </record>

    <record id="account_peppol_view_account_invoice_filter" model="ir.ui.view">
        <field name="name">account.invoice.select.inherit</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_account_invoice_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//search/group/filter[@name='status']" position="after">
                <filter string="Peppol status" name="peppol_move_state" context="{'group_by': 'peppol_move_state'}"/>
            </xpath>
            <xpath expr="//filter[@name='to_check']" position='after'>
                <separator/>
                <filter name="peppol_ready"
                        string="Peppol Ready"
                        domain="[('state', '=', 'posted'), ('peppol_move_state', '=', 'ready'), ('move_type', 'in', ('out_invoice', 'out_refund', 'out_receipt'))]"/>
                <separator/>
            </xpath>
        </field>
    </record>
</odoo>
