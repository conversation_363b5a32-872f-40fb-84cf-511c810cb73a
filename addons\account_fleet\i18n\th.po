# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_fleet
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_log_services_view_form
msgid ""
"<span class=\"o_stat_text text-success\" invisible=\"account_move_state != 'posted'\" title=\"Service's Bill\">Service's Bill</span>\n"
"                            <span class=\"o_stat_text text-warning\" invisible=\"account_move_state == 'posted'\" title=\"Service's Bill\">Service's Bill</span>"
msgstr ""
"<span class=\"o_stat_text text-success\" invisible=\"account_move_state != 'posted'\" title=\"Service's Bill\">ใบเรียกเก็บเงินการบริการ</span>\n"
"                            <span class=\"o_stat_text text-warning\" invisible=\"account_move_state == 'posted'\" title=\"Service's Bill\">ใบเรียกเก็บเงินการบริการ</span>"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__account_move_ids
msgid "Account Move"
msgstr "ย้ายบัญชี"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__account_move_line_id
msgid "Account Move Line"
msgstr "บรรทัดการย้ายบัญชี"

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/fleet_vehicle_log_services.py:0
msgid "Bill"
msgstr "บิล"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "Bills"
msgstr "ใบเรียกเก็บเงิน"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__bill_count
msgid "Bills Count"
msgstr "จำนวนใบเรียกเก็บเงิน"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__amount
msgid "Cost"
msgstr "ต้นทุน"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_automatic_entry_wizard
msgid "Create Automatic Entries"
msgstr "สร้างรายการอัตโนมัติ"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.account_move_view_tree
msgid "Creation Date"
msgstr "วันที่สร้าง"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move
msgid "Journal Entry"
msgstr "รายการสมุดรายวัน"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move_line
msgid "Journal Item"
msgstr "รายการสมุดรายวัน"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__need_vehicle
msgid "Need Vehicle"
msgstr "ต้องการยานพาหนะ"

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/account_move.py:0
msgid "Service Vendor Bill: %s"
msgstr "ใบเรียกเก็บเงินผู้ให้บริการ: %s"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "บริการสำหรับยานพาหนะ"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__account_move_state
msgid "Status"
msgstr "สถานะ"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__vehicle_id
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__vehicle_id
msgid "Vehicle"
msgstr "ยานพาหนะ"

#. module: account_fleet
#: model:fleet.service.type,name:account_fleet.data_fleet_service_type_vendor_bill
msgid "Vendor Bill"
msgstr "ใบเรียกเก็บเงินของผู้ขาย"

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/fleet_vehicle_log_services.py:0
msgid ""
"You cannot delete log services records because one or more of them were bill"
" created."
msgstr ""
"คุณไม่สามารถลบบันทึกบริการบันทึกได้เนื่องจากมีการสร้างใบเรียกเก็บเงินอย่างน้อยหนึ่งรายการ"

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/fleet_vehicle_log_services.py:0
msgid ""
"You cannot modify amount of services linked to an account move line. Do it "
"on the related accounting entry instead."
msgstr ""
"คุณไม่สามารถแก้ไขจำนวนบริการที่เชื่อมโยงกับบรรทัดการย้ายบัญชีได้ "
"ให้ทำในรายการบัญชีที่เกี่ยวข้องแทน"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "show the vendor bills for this vehicle"
msgstr "แสดงใบเรียกเก็บเงินผู้ขายสำหรับยานพาหนะคันนี้"
