<?xml version="1.0"?>
<odoo>

    <record id="view_hr_holidays_filter_report" model="ir.ui.view">
        <field name="name">hr.holidays.filter</field>
        <field name="model">hr.leave.report</field>
        <field name="arch" type="xml">
            <search string="Search Time Off">
                <field name="employee_id"/>
                <field name="name"/>
                <filter domain="[('state','in',('confirm','validate1'))]" string="To Approve" name="approve"/>
                <filter string="Approved Requests" domain="[('state', '=', 'validate')]" name="validated"/>
                <separator/>
                <filter string="Time off" domain="[('leave_type', '=', 'request')]" name="leave_type_timeoff"/>
                <filter string="Allocations" domain="[('leave_type', '=', 'allocation')]" name="leave_type_allocations"/>
                <separator/>
                <filter string="My Department" name="department" domain="[('department_id.manager_id.user_id', '=', uid)]" help="My Department"/>
                <separator/>
                <filter name="year" date="date_from" default_period="year" string="Current Year"/>
                <separator/>
                <filter string="My Requests" name="my_leaves" domain="[('employee_id.user_id', '=', uid)]"/>
                <field name="department_id" operator="child_of"/>
                <field name="holiday_status_id"/>
                <group expand="0" string="Group By">
                    <filter name="group_employee" string="Employee" context="{'group_by':'employee_id'}"/>
                    <filter name="group_type" string="Type" context="{'group_by':'holiday_status_id'}"/>
                    <filter name="group_company" string="Company" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                    <separator/>
                    <filter name="group_date_from" string="Start Date" context="{'group_by':'date_from'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="hr_leave_report_tree" model="ir.ui.view">
        <field name="name">report.hr.holidays.report.leave_all.list</field>
        <field name="model">hr.leave.report</field>
        <field name="arch" type="xml">
            <list create="0" edit="0" delete="0" action="action_open_record" type="object">
                <field name="employee_id"/>
                <field name="number_of_days" string="Number of Days" sum="Remaining Days"/>
                <field name="number_of_hours" string="Number of Hours" sum="Remaining Hours"/>
                <field name="leave_type"/>
                <field name="date_from"/>
                <field name="date_to"/>
                <field name="state"/>
                <field name="name"/>
            </list>
        </field>
    </record>

    <record id="hr_leave_report_graph" model="ir.ui.view">
        <field name="name">report.hr.holidays.report.leave_all.graph</field>
        <field name="model">hr.leave.report</field>
        <field name="arch" type="xml">
            <graph string="Time off Summary" sample="1">
                <field name="employee_id" type="row"/>
                <field name="number_of_days" string="Duration (Days)" type="measure"/>
                <field name="number_of_hours" string="Duration (Hours)"/>
            </graph>
        </field>
    </record>

    <record id="hr_leave_report_pivot" model="ir.ui.view">
        <field name="name">report.hr.holidays.report.leave_all.pivot</field>
        <field name="model">hr.leave.report</field>
        <field name="arch" type="xml">
            <pivot string="Time off Summary" sample="1">
                <field name="employee_id"/>
                <field name="number_of_days"/>
                <field name="number_of_hours" type="measure"/>
            </pivot>
        </field>
    </record>

    <record id="action_hr_leave_report" model="ir.actions.act_window">
        <field name="name">Time Off Analysis</field>
        <field name="res_model">hr.leave.report</field>
        <field name="view_mode">graph,list,pivot</field>
        <field name="search_view_id" ref="view_hr_holidays_filter_report"/>
        <field name="domain">[]</field>
        <field name="context">{'search_default_group_type': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No data yet!
            </p>
        </field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No data to display
            </p>
        </field>
    </record>

    <record id="hr_leave_report_action" model="ir.actions.act_window">
        <field name="name">Time Off Analysis</field>
        <field name="res_model">hr.leave.report</field>
        <field name="view_mode">graph,pivot</field>
        <field name="context">{
            'search_default_department_id': [active_id],
            'default_department_id': active_id,
            'search_default_group_employee': 1,
            'search_default_group_type': 1,
            'search_default_group_date_from': 'month',
        }
        </field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No data yet!
            </p>
        </field>
    </record>
</odoo>
