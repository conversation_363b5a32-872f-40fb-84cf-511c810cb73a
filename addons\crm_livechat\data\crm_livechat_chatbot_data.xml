<?xml version="1.0" encoding="UTF-8" ?>
<odoo><data noupdate="1">

    <!--
        This provides a working lead generation chatbot for people to work with.
        It's placed into 'data' to give them a starting point.
        From that record, they can duplicate / adapt / delete / ...
    -->

    <record id="chatbot_script_lead_generation_bot" model="chatbot.script">
        <field name="title">Lead Generation Bot</field>
        <field name="image_1920" type="base64" file="mail/static/src/img/odoobot.png"/>
    </record>

    <record id="chatbot_script_lead_generation_step_welcome" model="chatbot.script.step">
        <field name="message">Hi there, what brings you to our website today? 👋</field>
        <field name="sequence">1</field>
        <field name="step_type">free_input_multi</field>
        <field name="chatbot_script_id" ref="chatbot_script_lead_generation_bot"/>
    </record>

    <record id="chatbot_script_lead_generation_step_forward_operator" model="chatbot.script.step">
        <field name="sequence">2</field>
        <field name="step_type">forward_operator</field>
        <field name="chatbot_script_id" ref="chatbot_script_lead_generation_bot"/>
    </record>

    <record id="chatbot_script_lead_generation_step_noone_available" model="chatbot.script.step">
        <field name="message">Hu-ho, it looks like none of our operators are available 🙁</field>
        <field name="sequence">3</field>
        <field name="step_type">text</field>
        <field name="chatbot_script_id" ref="chatbot_script_lead_generation_bot"/>
    </record>

    <record id="chatbot_script_welcome_step_pricing_email" model="chatbot.script.step">
        <field name="message">Would you mind leaving your email address so that we can reach you back?</field>
        <field name="sequence">4</field>
        <field name="step_type">question_email</field>
        <field name="chatbot_script_id" ref="chatbot_script_lead_generation_bot"/>
    </record>

    <record id="chatbot_script_welcome_step_just_looking" model="chatbot.script.step">
        <field name="message">Thank you, you should hear back from us very soon!</field>
        <field name="sequence">5</field>
        <field name="step_type">create_lead</field>
        <field name="chatbot_script_id" ref="chatbot_script_lead_generation_bot"/>
    </record>

</data></odoo>
