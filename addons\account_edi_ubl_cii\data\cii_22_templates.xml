<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="account_invoice_line_facturx_export_22">
            <t t-set="line" t-value="line_vals['line']"/>
            <t  xmlns:ram="urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:100"
                xmlns:rsm="urn:un:unece:uncefact:data:standard:CrossIndustryInvoice:100"
                xmlns:udt="urn:un:unece:uncefact:data:standard:UnqualifiedDataType:100">

                <ram:IncludedSupplyChainTradeLineItem>
                    <!-- Line number. -->
                    <ram:AssociatedDocumentLineDocument>
                        <ram:LineID t-out="line_vals.get('index')"/>
                    </ram:AssociatedDocumentLineDocument>

                    <!-- Product. -->
                    <ram:SpecifiedTradeProduct>
                        <ram:GlobalID
                            schemeID="0160"
                            t-if="line.product_id and line.product_id.barcode"
                            t-out="line.product_id.barcode"/>
                        <ram:SellerAssignedID
                            t-if="line.product_id and line.product_id.default_code"
                            t-out="line.product_id.default_code"/>
                        <ram:Name t-out="line.product_id.name"/>
                        <ram:Description t-out="line.name" t-if="line.name != line.product_id.name"/>
                    </ram:SpecifiedTradeProduct>

                    <!-- Amounts. -->
                    <ram:SpecifiedLineTradeAgreement>
                        <!-- Line information: unit_price
                        NB: the gross_price_unit should be the price tax excluded!
                        if price_unit = 100 and tax 10% price_include -> then the gross_price_unit is 91
                        -->
                        <ram:GrossPriceProductTradePrice>
                            <ram:ChargeAmount t-out="format_monetary(line_vals['gross_price_total_unit'], 2)"/>
                            <!-- Discount. -->
                            <ram:AppliedTradeAllowanceCharge t-if="line.discount">
                                <ram:ChargeIndicator>
                                    <udt:Indicator t-translation="off">false</udt:Indicator>
                                </ram:ChargeIndicator>
                                <ram:ActualAmount t-out="format_monetary(line_vals['price_discount_unit'], 2)"/>
                            </ram:AppliedTradeAllowanceCharge>
                        </ram:GrossPriceProductTradePrice>
                        <!-- Line unit price, with discount applied -->
                        <ram:NetPriceProductTradePrice>
                            <ram:ChargeAmount
                                t-out="format_monetary(line_vals['price_subtotal_unit'], 2)"/>
                        </ram:NetPriceProductTradePrice>
                    </ram:SpecifiedLineTradeAgreement>

                    <!-- Quantity. -->
                    <ram:SpecifiedLineTradeDelivery>
                        <ram:BilledQuantity
                                t-att-unitCode="line_vals.get('unece_uom_code')"
                                t-out="line_vals.get('quantity')"/>
                    </ram:SpecifiedLineTradeDelivery>

                    <ram:SpecifiedLineTradeSettlement>
                        <t t-foreach="tax_details['tax_details_per_record'][line]['tax_details'].values()"
                           t-as="tax_detail_vals">
                            <ram:ApplicableTradeTax t-if="tax_detail_vals['amount_type'] == 'percent'">
                                <ram:TypeCode t-translation="off">VAT</ram:TypeCode>
                                <ram:CategoryCode t-out="tax_detail_vals['tax_category_code']"/>
                                <ram:RateApplicablePercent t-out="tax_detail_vals['amount']"/>
                            </ram:ApplicableTradeTax>
                        </t>

                        <!-- Allowance/Charge on the line -->
                        <t t-foreach="line_vals.get('allowance_charge_vals_list')" t-as="allowance_charge_vals">
                            <ram:SpecifiedTradeAllowanceCharge>
                                <ram:ChargeIndicator>
                                    <udt:Indicator t-esc="allowance_charge_vals['indicator']"/>
                                </ram:ChargeIndicator>
                                <ram:ActualAmount t-esc="format_monetary(allowance_charge_vals['amount'], 2)"/>
                                <ram:ReasonCode t-esc="allowance_charge_vals['reason_code']"/>
                                <ram:Reason t-esc="allowance_charge_vals['reason']"/>
                            </ram:SpecifiedTradeAllowanceCharge>
                        </t>

                        <!-- Subtotal. -->
                        <ram:SpecifiedTradeSettlementLineMonetarySummation>
                            <ram:LineTotalAmount t-esc="format_monetary(line_vals['line_total_amount'], 2)"/>
                        </ram:SpecifiedTradeSettlementLineMonetarySummation>

                    </ram:SpecifiedLineTradeSettlement>
                </ram:IncludedSupplyChainTradeLineItem>
            </t>
        </template>

        <template id="account_invoice_partner_facturx_export_22">
            <t  xmlns:ram="urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:100"
                xmlns:rsm="urn:un:unece:uncefact:data:standard:CrossIndustryInvoice:100"
                xmlns:udt="urn:un:unece:uncefact:data:standard:UnqualifiedDataType:100">
                <!-- Contact. -->
                <ram:Name t-out="partner.display_name"/>
                <ram:SpecifiedLegalOrganization t-if="specified_legal_organization_val">
                    <ram:ID t-att-schemeID="str('0002')"
                            t-out="specified_legal_organization_val"/>
                </ram:SpecifiedLegalOrganization>

                <ram:DefinedTradeContact t-if="not hide_dtc">
                    <ram:PersonName t-out="partner.name"/>
                    <ram:TelephoneUniversalCommunication t-if="partner.phone or partner.mobile">
                        <ram:CompleteNumber t-out="partner.phone or partner.mobile"/>
                    </ram:TelephoneUniversalCommunication>
                    <ram:EmailURIUniversalCommunication t-if="partner.email">
                        <ram:URIID t-out="partner.email"/>
                    </ram:EmailURIUniversalCommunication>
                </ram:DefinedTradeContact>

                <!-- Address. -->
                <t t-call="account_edi_ubl_cii.account_invoice_address_facturx_export_22"/>
            </t>
        </template>

        <template id="account_invoice_address_facturx_export_22">
            <t  xmlns:ram="urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:100"
                xmlns:rsm="urn:un:unece:uncefact:data:standard:CrossIndustryInvoice:100"
                xmlns:udt="urn:un:unece:uncefact:data:standard:UnqualifiedDataType:100">
                <!-- Address. -->
                <ram:PostalTradeAddress>
                    <ram:PostcodeCode t-out="partner.zip"/>
                    <ram:LineOne t-out="partner.street"/>
                    <ram:LineTwo t-out="partner.street2"/>
                    <ram:CityName t-out="partner.city"/>
                    <ram:CountryID t-out="partner.country_id.code"/>
                </ram:PostalTradeAddress>
            </t>
        </template>

        <template id="account_invoice_facturx_export_22">
            <t t-set="currency" t-value="record.currency_id or record.company_currency_id"/>
            <rsm:CrossIndustryInvoice
                xmlns:ram="urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:100"
                xmlns:rsm="urn:un:unece:uncefact:data:standard:CrossIndustryInvoice:100"
                xmlns:udt="urn:un:unece:uncefact:data:standard:UnqualifiedDataType:100">
                <!-- Factur-x level:
                    * minimum or basicwl:   urn:factur-x.eu:1p0...
                    * basic:                urn:cen.eu:en16931:2017:compliant:factur-x.eu:1p0:basic
                    * en16931:              urn:cen.eu:en16931:2017
                -->
                <rsm:ExchangedDocumentContext>
                    <ram:GuidelineSpecifiedDocumentContextParameter>
                        <ram:ID t-out="document_context_id"/>
                    </ram:GuidelineSpecifiedDocumentContextParameter>
                </rsm:ExchangedDocumentContext>

                <!-- Document Headers. -->
                <rsm:ExchangedDocument>
                    <ram:ID t-out="ExchangedDocument_vals.get('id')"/>
                    <ram:TypeCode t-out="ExchangedDocument_vals.get('type_code')"/>
                    <ram:IssueDateTime t-if="ExchangedDocument_vals.get('issue_date_time')">
                        <udt:DateTimeString format="102" t-out="format_date(ExchangedDocument_vals.get('issue_date_time'))"/>
                    </ram:IssueDateTime>
                    <ram:IncludedNote>
                        <ram:Content t-out="ExchangedDocument_vals.get('included_note')"/>
                    </ram:IncludedNote>
                </rsm:ExchangedDocument>

                <rsm:SupplyChainTradeTransaction>
                    <!-- Invoice lines. -->
                    <t t-foreach="invoice_line_vals_list" t-as="line_vals">
                        <t t-call="account_edi_ubl_cii.account_invoice_line_facturx_export_22"/>
                    </t>

                    <!-- Partners. -->
                    <ram:ApplicableHeaderTradeAgreement>
                        <!-- "Service Exécutant" (Used by Chorus Pro) -->
                        <ram:BuyerReference t-out="buyer_reference"/>
                        <!-- Seller. -->
                        <ram:SellerTradeParty>
                            <!-- Address. -->
                            <t t-call="account_edi_ubl_cii.account_invoice_partner_facturx_export_22">
                                <t t-set="partner" t-value="record.company_id.partner_id"/>
                                <t t-set="specified_legal_organization_val" t-value="seller_specified_legal_organization"/>
                            </t>

                            <!-- VAT. -->
                            <ram:SpecifiedTaxRegistration t-if="record.company_id.vat">
                                <ram:ID schemeID="VA" t-out="record.company_id.vat"/>
                            </ram:SpecifiedTaxRegistration>
                        </ram:SellerTradeParty>

                        <!-- Customer. -->
                        <ram:BuyerTradeParty>
                            <!-- Address. -->
                            <t t-call="account_edi_ubl_cii.account_invoice_partner_facturx_export_22">
                                <t t-set="partner" t-value="record.partner_id"/>
                                <t t-set="specified_legal_organization_val" t-value="buyer_specified_legal_organization"/>
                            </t>

                            <!-- VAT. -->
                            <ram:SpecifiedTaxRegistration t-if="record.commercial_partner_id.vat">
                                <ram:ID schemeID="VA" t-out="record.commercial_partner_id.vat"/>
                            </ram:SpecifiedTaxRegistration>
                        </ram:BuyerTradeParty>

                        <!-- Reference. -->
                        <ram:BuyerOrderReferencedDocument>
                            <!-- "Engagement Juridique" (used by Chorus Pro) -->
                            <ram:IssuerAssignedID t-out="purchase_order_reference"/>
                        </ram:BuyerOrderReferencedDocument>
                        <ram:ContractReferencedDocument>
                            <!-- "Numéro de Marché" (Used by Chorus Pro) -->
                            <ram:IssuerAssignedID t-out="contract_reference"/>
                        </ram:ContractReferencedDocument>
                    </ram:ApplicableHeaderTradeAgreement>

                    <!-- Delivery -->
                    <ram:ApplicableHeaderTradeDelivery>
                        <ram:ShipToTradeParty>
                            <t t-call="account_edi_ubl_cii.account_invoice_partner_facturx_export_22">
                                <t t-set="partner"
                                   t-value="ship_to_trade_party"/>
                                <t t-set="hide_dtc" t-value="document_context_id == 'urn:cen.eu:en16931:2017#compliant#urn:xoev-de:kosit:standard:xrechnung_2.2'"/>
                            </t>
                        </ram:ShipToTradeParty>

                        <ram:ActualDeliverySupplyChainEvent t-if="scheduled_delivery_time">
                            <ram:OccurrenceDateTime>
                                <udt:DateTimeString t-att-format="102"
                                                    t-out="format_date(scheduled_delivery_time)"/>
                            </ram:OccurrenceDateTime>
                        </ram:ActualDeliverySupplyChainEvent>
                    </ram:ApplicableHeaderTradeDelivery>

                    <!-- Taxes. -->
                    <ram:ApplicableHeaderTradeSettlement>

                        <ram:PaymentReference t-out="record.payment_reference"/>
                        <ram:InvoiceCurrencyCode t-out="currency.name"/>

                        <!-- Bank account. -->
                        <ram:SpecifiedTradeSettlementPaymentMeans t-if="record.partner_bank_id.sanitized_acc_number">
                            <ram:TypeCode>42</ram:TypeCode>
                            <ram:PayeePartyCreditorFinancialAccount>
                                <ram:IBANID
                                    t-if="record.partner_bank_id.acc_type == 'iban'"
                                    t-out="record.partner_bank_id.sanitized_acc_number"/>
                                <ram:ProprietaryID
                                    t-if="record.partner_bank_id.acc_type != 'iban'"
                                    t-out="record.partner_bank_id.sanitized_acc_number"/>
                            </ram:PayeePartyCreditorFinancialAccount>
                        </ram:SpecifiedTradeSettlementPaymentMeans>

                        <!-- Tax Summary. -->
                        <t t-foreach="tax_details.get('tax_details', {}).values()" t-as="tax_detail_vals">
                            <ram:ApplicableTradeTax>
                                <ram:CalculatedAmount
                                    t-out="format_monetary(tax_detail_vals.get('calculated_amount'), 2)"/>
                                <ram:TypeCode t-translation="off">VAT</ram:TypeCode>
                                <ram:ExemptionReason t-out="tax_detail_vals.get('tax_exemption_reason')"/>
                                <ram:BasisAmount
                                    t-out="format_monetary(tax_detail_vals['base_amount_currency'], 2)"/>
                                <ram:CategoryCode t-out="tax_detail_vals['tax_category_code']"/>
                                <ram:ExemptionReasonCode t-out="tax_detail_vals['tax_exemption_reason_code']"/>
                                <!-- 5 = Tax is exigible on the date on the invoice -->
                                <ram:DueDateTypeCode>5</ram:DueDateTypeCode>
                                <ram:RateApplicablePercent
                                    t-if="tax_detail_vals['amount_type'] == 'percent'"
                                    t-out="tax_detail_vals['amount']"/>
                            </ram:ApplicableTradeTax>
                        </t>

                        <!-- Billing Period -->
                        <ram:BillingSpecifiedPeriod t-if="billing_start and billing_end">
                            <ram:StartDateTime>
                                <udt:DateTimeString format="102" t-out="format_date(billing_start)"/>
                            </ram:StartDateTime>
                            <ram:EndDateTime>
                                <udt:DateTimeString format="102" t-out="format_date(billing_end)"/>
                            </ram:EndDateTime>
                        </ram:BillingSpecifiedPeriod>

                        <!-- Payment Term. -->
                        <ram:SpecifiedTradePaymentTerms>
                            <ram:Description t-if="record.invoice_payment_term_id" t-out="record.invoice_payment_term_id.name"/>
                            <ram:DueDateDateTime t-if="record.invoice_date_due">
                                <udt:DateTimeString format="102" t-out="format_date(record.invoice_date_due)"/>
                            </ram:DueDateDateTime>
                        </ram:SpecifiedTradePaymentTerms>

                        <!-- Summary. -->
                        <ram:SpecifiedTradeSettlementHeaderMonetarySummation>
                            <ram:LineTotalAmount
                                t-esc="format_monetary(tax_basis_total_amount, 2)"/>
                            <ram:TaxBasisTotalAmount
                                t-esc="format_monetary(tax_basis_total_amount, 2)"/>
                            <ram:TaxTotalAmount
                                t-att-currencyID="currency.name"
                                t-esc="format_monetary(tax_total_amount, 2)"/>
                            <ram:GrandTotalAmount
                                t-out="format_monetary(record.amount_total, 2)"/>
                            <ram:TotalPrepaidAmount
                                t-out="format_monetary(record.amount_total - record.amount_residual, 2)"/>
                            <ram:DuePayableAmount
                                t-out="format_monetary(record.amount_residual, 2)"/>
                        </ram:SpecifiedTradeSettlementHeaderMonetarySummation>
                    </ram:ApplicableHeaderTradeSettlement>
                </rsm:SupplyChainTradeTransaction>
            </rsm:CrossIndustryInvoice>
        </template>

        <template id="account_invoice_pdfa_3_facturx_metadata">
            <x:xmpmeta xmlns:x="adobe:ns:meta/">
                <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
                    <rdf:Description xmlns:pdfaid="http://www.aiim.org/pdfa/ns/id/" rdf:about="">
                        <pdfaid:part>3</pdfaid:part>
                        <pdfaid:conformance>B</pdfaid:conformance>
                    </rdf:Description>
                    <rdf:Description xmlns:dc="http://purl.org/dc/elements/1.1/" rdf:about="">
                        <dc:title>
                            <rdf:Alt>
                                <rdf:li t-att="{'xml:lang': 'x-default'}" t-out="title"/>
                            </rdf:Alt>
                        </dc:title>
                        <dc:creator>
                            <rdf:Seq>
                                <rdf:li>Odoo</rdf:li>
                            </rdf:Seq>
                        </dc:creator>
                        <dc:description>
                            <rdf:Alt>
                                <rdf:li t-att="{'xml:lang': 'x-default'}">Invoice generated by Odoo</rdf:li>
                            </rdf:Alt>
                        </dc:description>
                    </rdf:Description>
                    <rdf:Description xmlns:pdf="http://ns.adobe.com/pdf/1.3/" rdf:about="">
                        <pdf:Producer>Odoo</pdf:Producer>
                    </rdf:Description>
                    <rdf:Description xmlns:xmp="http://ns.adobe.com/xap/1.0/" rdf:about="">
                        <xmp:CreatorTool>Odoo</xmp:CreatorTool>
                        <xmp:CreateDate t-out="date"/>
                        <xmp:ModifyDate t-out="date"/>
                    </rdf:Description>
                    <rdf:Description xmlns:pdfaExtension="http://www.aiim.org/pdfa/ns/extension/"
                                     xmlns:pdfaSchema="http://www.aiim.org/pdfa/ns/schema#"
                                     xmlns:pdfaProperty="http://www.aiim.org/pdfa/ns/property#" rdf:about="">
                        <pdfaExtension:schemas>
                            <rdf:Bag>
                                <rdf:li rdf:parseType="Resource">
                                    <pdfaSchema:schema t-translation="off">Factur-X PDFA Extension Schema</pdfaSchema:schema>
                                    <pdfaSchema:namespaceURI>urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#</pdfaSchema:namespaceURI>
                                    <pdfaSchema:prefix>fx</pdfaSchema:prefix>
                                    <pdfaSchema:property>
                                        <rdf:Seq>
                                            <rdf:li rdf:parseType="Resource">
                                                <pdfaProperty:name t-translation="off">DocumentFileName</pdfaProperty:name>
                                                <pdfaProperty:valueType t-translation="off">Text</pdfaProperty:valueType>
                                                <pdfaProperty:category t-translation="off">external</pdfaProperty:category>
                                                <pdfaProperty:description t-translation="off">name of the embedded XML invoice file</pdfaProperty:description>
                                            </rdf:li>
                                            <rdf:li rdf:parseType="Resource">
                                                <pdfaProperty:name t-translation="off">DocumentType</pdfaProperty:name>
                                                <pdfaProperty:valueType t-translation="off">Text</pdfaProperty:valueType>
                                                <pdfaProperty:category t-translation="off">external</pdfaProperty:category>
                                                <pdfaProperty:description t-translation="off">INVOICE</pdfaProperty:description>
                                            </rdf:li>
                                            <rdf:li rdf:parseType="Resource">
                                                <pdfaProperty:name t-translation="off">Version</pdfaProperty:name>
                                                <pdfaProperty:valueType t-translation="off">Text</pdfaProperty:valueType>
                                                <pdfaProperty:category t-translation="off">external</pdfaProperty:category>
                                                <pdfaProperty:description t-translation="off">The actual version of the Factur-X XML schema</pdfaProperty:description>
                                            </rdf:li>
                                            <rdf:li rdf:parseType="Resource">
                                                <pdfaProperty:name t-translation="off">ConformanceLevel</pdfaProperty:name>
                                                <pdfaProperty:valueType t-translation="off">Text</pdfaProperty:valueType>
                                                <pdfaProperty:category t-translation="off">external</pdfaProperty:category>
                                                <pdfaProperty:description t-translation="off">The conformance level of the embedded Factur-X data</pdfaProperty:description>
                                            </rdf:li>
                                        </rdf:Seq>
                                    </pdfaSchema:property>
                                </rdf:li>
                            </rdf:Bag>
                        </pdfaExtension:schemas>
                    </rdf:Description>
                    <rdf:Description xmlns:fx="urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#" rdf:about="">
                        <fx:ConformanceLevel>EN 16931</fx:ConformanceLevel>
                        <fx:DocumentFileName>factur-x.xml</fx:DocumentFileName>
                        <fx:DocumentType t-translation="off">INVOICE</fx:DocumentType>
                        <fx:Version>1.0</fx:Version>
                    </rdf:Description>
                </rdf:RDF>
            </x:xmpmeta>
        </template>

    </data>
</odoo>
