<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_account_payment_register_form_inherit_payment" model="ir.ui.view">
        <field name="name">account.payment.register.form.inherit.payment</field>
        <field name="model">account.payment.register</field>
        <field name="inherit_id" ref="account.view_account_payment_register_form"/>
        <field name="arch" type="xml">
            <field name="payment_method_line_id" position="after">
                <field name="payment_method_code" invisible="1"/>
                <field name="suitable_payment_token_ids" invisible="1"/>
                <field name="use_electronic_payment_method" invisible="1"/>
                <field name="payment_token_id"
                       options="{'no_create': True}"
                       invisible="not use_electronic_payment_method or not can_edit_wizard or (can_group_payments and not group_payment)"/>
            </field>
        </field>
    </record>

</odoo>
