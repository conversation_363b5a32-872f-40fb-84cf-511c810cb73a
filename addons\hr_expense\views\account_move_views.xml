<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_move_form_inherit_expense" model="ir.ui.view">
            <field name="name">account.move.form.inherit</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='button_box']" position="inside">
                    <field name="expense_sheet_id" invisible="1"/>
                    <button name="action_open_expense_report"
                            class="oe_stat_button"
                            icon="fa-file-text-o"
                            type="object"
                            invisible="not expense_sheet_id">
                            <div class="o_stat_info">
                                <span class="o_stat_text">Expense Report</span>
                            </div>
                    </button>
                </xpath>

                <xpath expr="//sheet" position="before">
                    <field name="show_commercial_partner_warning" invisible="1"/>
                    <div class="alert alert-warning" role="alert"
                         invisible="not show_commercial_partner_warning">
                        Do you really want to invoice your own company? Remove the "Company Name" from the partner to fix the configuration. Cancel this invoice and start again.
                    </div>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
