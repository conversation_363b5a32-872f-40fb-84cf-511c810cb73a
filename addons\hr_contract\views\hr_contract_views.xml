<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <record id="hr_hr_employee_view_form2" model="ir.ui.view">
            <field name="name">hr.hr.employee.view.form2</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <data>
                    <xpath expr="//sheet" position="inside">
                        <field name="contract_warning" invisible="1"/>
                    </xpath>
                    <xpath expr="//page[@name='hr_settings']//field[@name='employee_type']" position="after">
                        <field name="first_contract_date" invisible="1"/>
                    </xpath>
                    <xpath expr="//field[@name='resource_calendar_id']" position="replace">
                        <field name="calendar_mismatch" invisible="1"/>
                        <label for="resource_calendar_id"/>
                        <div class="d-flex align-items-center">
                            <field name="resource_calendar_id" help="The default working hours are set in configuration." placeholder="Fully Flexible"/>
                            <widget name="contract_warning_tooltip"
                                invisible="not calendar_mismatch"/>
                        </div>
                    </xpath>
                </data>
            </field>
        </record>

        <!-- This part of the view is defined separately as we want to give priority to the smartbutton. -->
        <record id="hr_hr_employee_view_form3" model="ir.ui.view">
            <field name="name">hr.hr.employee.view.form3</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="priority" eval="2" />
            <field name="arch" type="xml">
                <data>
                    <div name="button_box" position="inside">
                        <button name="action_open_contract"
                            class="oe_stat_button"
                            icon="fa-book"
                            type="object"
                            groups="hr_contract.group_hr_contract_manager"
                            context="{
                                'default_employee_id': id,
                                'default_resource_calendar_id': resource_calendar_id.id or False,
                                'from_action_open_contract': True,
                            }"
                            invisible="employee_type not in ['employee', 'student', 'trainee']">
                            <div invisible="not first_contract_date" class="o_stat_info">
                                <span class="o_stat_text text-success" invisible="contract_warning" title="In Contract Since"> In Contract Since</span>
                                <span class="o_stat_value text-success" invisible="contract_warning">
                                    <field name="first_contract_date" readonly="1"/>
                                </span>
                                <span class="o_stat_text text-danger" invisible="not contract_warning" title="In Contract Since">
                                    In Contract Since
                                </span>
                                <span class="o_stat_value text-danger" invisible="not contract_warning">
                                    <field name="first_contract_date" readonly="1"/>
                                </span>
                            </div>
                            <div invisible="first_contract_date" class="o_stat_info">
                                <span class="o_stat_value text-danger">
                                    <field name="contracts_count"/>
                                </span>
                                <span invisible="contracts_count != 1" class="o_stat_text text-danger" >
                                    Contract
                                </span>
                                <span invisible="contracts_count == 1" class="o_stat_text text-danger">
                                    Contracts
                                </span>
                            </div>
                        </button>
                    </div>
                </data>
            </field>
        </record>

        <record id="hr_employee_view_search" model="ir.ui.view">
            <field name="name">hr.employee.view.search</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_filter"/>
            <field name="arch" type="xml">
                <data>
                    <xpath expr="//filter[@name='inactive']" position="before">
                        <separator/>
                        <filter string="Contract Warning" name="with_contract_warning" domain="[('contract_warning', '=', True)]"/>
                        <separator/>
                    </xpath>
                </data>
            </field>
        </record>

        <record id="hr_user_view_form" model="ir.ui.view">
            <field name="name">hr.user.preferences.view.form.contract.inherit</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="hr.res_users_view_form_profile"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='employee_bank_account_id']" position="replace">
                    <field name="employee_bank_account_id" context="{'display_partner':True}" readonly="not can_edit"/>
                </xpath>
            </field>
        </record>

        <record id="hr_contract_view_search" model="ir.ui.view">
            <field name="name">hr.contract.search</field>
            <field name="model">hr.contract</field>
            <field name="arch" type="xml">
                <search string="Search Contract">
                    <field name="name" string="Contract"/>
                    <field name="date_start"/>
                    <field name="date_end"/>
                    <field name="employee_id"/>
                    <field name="job_id"/>
                    <field name="department_id" operator="child_of"/>
                    <field name="resource_calendar_id"/>
                    <filter string="Running Contracts" name="running" domain="[('state', '=', 'open')]"/>
                    <filter string="Contracts to review" name="not_running" domain="[('state', 'in', ['draft', 'close'])]"/>
                    <separator />
                    <filter string="Start Date" name="start_date" date="date_start"/>
                    <filter string="End Date" name="end_date" date="date_end"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <filter string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which have a next action date before today"/>
                    <filter string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Status" name="group_by_state" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Employee" name="group_by_employee" domain="[]" context="{'group_by': 'employee_id'}"/>
                        <filter string="Start Date" name="group_by_date_start" domain="[]" context="{'group_by': 'date_start'}"/>
                        <filter string="Job Position" name="group_by_job" domain="[]" context="{'group_by': 'job_id'}"/>
                        <filter string="Department" name="group_by_department" domain="[]" context="{'group_by': 'department_id'}"/>
                        <filter string="Working Schedule" name="group_by_resource_calendar_id" domain="[]" context="{'group_by': 'resource_calendar_id'}"/>
                        <filter string="Salary Structure Type" name="group_by_structure_type_id" domain="[]" context="{'group_by': 'structure_type_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="hr_contract_view_form" model="ir.ui.view">
            <field name="name">hr.contract.form</field>
            <field name="model">hr.contract</field>
            <field name="arch" type="xml">
                <form string="Current Contract">
                    <field name="contracts_count" invisible="1"/>
                    <header invisible="not id">
                        <field name="state" groups="!hr_contract.group_hr_contract_manager" widget="statusbar"/>
                        <field name="state" groups="hr_contract.group_hr_contract_manager" widget="statusbar" options="{'clickable': '1'}"/>
                    </header>
                    <sheet>
                        <field name="state" invisible="1"/>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_open_contract_list"
                                class="oe_stat_button"
                                icon="fa-book"
                                type="object"
                                invisible="contracts_count == 0"
                                groups="hr_contract.group_hr_contract_employee_manager">
                                <div class="o_stat_info">
                                    <span class="o_stat_value">
                                        <field name="contracts_count"/>
                                    </span>
                                    <span invisible="contracts_count == 1" class="o_stat_text" >
                                        Contracts
                                    </span>
                                    <span invisible="contracts_count &gt; 1" class="o_stat_text">
                                        Contract
                                    </span>
                                </div>
                            </button>
                        </div>
                        <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                        <div class="oe_title pe-0 w-100 mw-100" name="title">
                            <h1 class="d-flex flex-row justify-content-between">
                                <field name="name" class="text-truncate" placeholder="Contract Reference"/>
                                <field name="kanban_state"
                                    class="d-flex align-items-center"
                                    groups="!hr_contract.group_hr_contract_manager"
                                    widget="state_selection" readonly="1"/>
                                <field name="kanban_state"
                                    class="d-flex align-items-center"
                                    groups="hr_contract.group_hr_contract_manager"
                                    widget="state_selection" readonly="0"/>
                            </h1>
                            <h2>
                                <field name="company_id" groups="base.group_multi_company" invisible="1"/>
                            </h2>
                        </div>
                        <group name="top_info">
                            <group name="top_info_left">
                                <field name="active" invisible="1"/>
                                <!-- employee_id = fields.Many2one('hr.employee', string='Employee', tracking=True, domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]") -->
                                <field name="company_id" invisible="1"/>
                                <field name="employee_id" widget="many2one_avatar_employee"/>
                                <field name="active_employee" invisible="1"/>
                                <field name="date_start" string="Contract Start Date"/>
                                <field name="date_end" string="Contract End Date"/>
                                <field name="company_country_id" invisible="1"/>
                                <field name="country_code" invisible="1"/>
                                <field name="calendar_mismatch" invisible="1"/>
                                <label for="resource_calendar_id"/>
                                <div id="resource_calendar_warning" class="d-flex align-items-center">
                                        <field name="resource_calendar_id"
                                            groups="!hr_contract.group_hr_contract_manager"
                                            placeholder="Fully Flexible"
                                            options="{'no_open': True, 'no_create': True}"/>
                                        <field name="resource_calendar_id"
                                            groups="hr_contract.group_hr_contract_manager"
                                            placeholder="Fully Flexible"/>
                                        <widget
                                            name="contract_warning_tooltip"
                                            invisible="not calendar_mismatch or state != 'open'"/>
                                </div>
                            </group>
                            <group name="top_info_right">
                                <field name="structure_type_id" groups="!hr_contract.group_hr_contract_manager" domain="['|', ('country_id', '=', False), ('country_id', '=', company_country_id)]" options="{'no_open': True, 'no_create': True}"/>
                                <field name="structure_type_id" groups="hr_contract.group_hr_contract_manager" domain="['|', ('country_id', '=', False), ('country_id', '=', company_country_id)]"/>
                                <field name="department_id" groups="!hr_contract.group_hr_contract_manager" options="{'no_open': True, 'no_create': True}"/>
                                <field name="department_id" groups="hr_contract.group_hr_contract_manager"/>
                                <field name="job_id" groups="!hr_contract.group_hr_contract_manager" options="{'no_open': True, 'no_create': True}"/>
                                <field name="job_id" groups="hr_contract.group_hr_contract_manager"/>
                                <field name="contract_type_id" groups="!hr_contract.group_hr_contract_manager" options="{'no_open': True, 'no_create': True}"/>
                                <field name="contract_type_id" groups="hr_contract.group_hr_contract_manager"/>
                                <field name="hr_responsible_id" widget="many2one_avatar_user" invisible="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Salary Information" name="information" class="o_hr_contract_salary_information">
                                <group name="salary_info">
                                    <group name="salary">
                                        <label for="wage"/>
                                        <div class="o_row mw-50" name="wage">
                                            <field name="wage" class="oe_inline o_hr_narrow_field" nolabel="1"/>
                                            <div class="mb-3" name="wage_period_label">/ month</div>
                                        </div>
                                    </group>
                                    <group name="yearly_benefits"/>
                                </group>
                            </page>
                            <page string="Details" name="other" groups="hr_contract.group_hr_contract_manager">
                                <group name="contract_details_0"/>
                                <group name="contract_details" col="2"/>
                                <group name="contract_details_2"/>
                                <group name="notes_group" string="Notes">
                                    <field name="notes" nolabel="1" placeholder="Type in notes about this contract..."/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter groups="hr_contract.group_hr_contract_manager"/>
                </form>
            </field>
        </record>

        <record id="hr_contract_view_tree" model="ir.ui.view">
            <field name="name">hr.contract.list</field>
            <field name="model">hr.contract</field>
            <field name="arch" type="xml">
                <list string="Contracts" multi_edit="1" sample="1" default_order='date_start DESC'>
                    <field name="company_id" column_invisible="True"/>
                    <field name="employee_id" readonly="1" widget="many2one_avatar_employee"/>
                    <field name="name" readonly="1"/>
                    <field name="department_id" readonly="1" optional="show"/>
                    <field name="job_id" optional="show"/>
                    <field name="date_start" readonly="1"/>
                    <field name="date_end" readonly="1"/>
                    <field name="contract_type_id" optional="show"/>
                    <field name="wage" widget="monetary" readonly="1" optional="hidden"/>
                    <field name="resource_calendar_id" optional="show"/>
                    <field name="structure_type_id" optional="hidden"/>
                    <field name="kanban_state" widget="state_selection" nolabel="1"/>
                    <field name="state" widget="badge" decoration-info="state == 'draft'" decoration-warning="state == 'close'" decoration-success="state == 'open'"/>
                    <field name="company_id" groups="base.group_multi_company" readonly="1" optional="hidden"/>
                </list>
            </field>
        </record>

        <record id="hr_contract_view_kanban" model="ir.ui.view">
            <field name="name">hr.contract.kanban</field>
            <field name="model">hr.contract</field>
            <field name="arch" type="xml">
                <kanban default_order="date_end" sample="1">
                    <field name="activity_state"/>
                    <progressbar field="activity_state" colors='{"planned": "success", "today": "warning", "overdue": "danger"}'/>
                    <templates>
                    <t t-name="menu" groups="hr_contract.group_hr_contract_manager">
                        <t t-if="widget.editable"><a role="menuitem" type="open" class="dropdown-item">Edit Contract</a></t>
                        <t t-if="widget.deletable"><a role="menuitem" type="delete" class="dropdown-item">Delete</a></t>
                    </t>
                    <t t-name="card">
                        <field class="fw-bold fs-5" name="name"/>
                        <field class="text-muted" name="job_id"/>
                        <field class="text-muted" name="department_id"/>
                        <div class="text-muted" name="div_date_id">
                            From <field name="date_start"/>
                            <t t-if="record.date_end.raw_value">
                                To <field name="date_end"/>
                            </t>
                        </div>
                        <footer>
                            <field name="activity_ids" widget="kanban_activity"/>
                            <div class="d-flex ms-auto">
                                <field class="mr4" name="kanban_state" widget="state_selection"/>
                                <field name="employee_id" widget="many2one_avatar_employee"/>
                            </div>
                        </footer>
                    </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="hr_contract_view_activity" model="ir.ui.view">
            <field name="name">hr.contract.activity</field>
            <field name="model">hr.contract</field>
            <field name="arch" type="xml">
                <activity string="Contracts">
                    <field name="employee_id"/>
                    <templates>
                        <div t-name="activity-box">
                            <img class="rounded" t-att-src="activity_image('hr.employee', 'avatar_128', record.employee_id.raw_value)" t-att-title="record.employee_id.value" t-att-alt="record.employee_id.value"/>
                            <div class="ms-2">
                                <field name="name" display="full" class="o_text_block"/>
                                <field name="job_id" muted="1" display="full" class="o_text_block"/>
                            </div>
                        </div>
                    </templates>
                </activity>
            </field>
        </record>

        <record id="action_hr_contract" model="ir.actions.act_window">
            <field name="name">Contracts</field>
            <field name="res_model">hr.contract</field>
            <field name="path">employee-contracts</field>
            <field name="view_mode">list,kanban,form,activity</field>
            <field name="domain">[('employee_id', '!=', False)]</field>
            <field name="context">{'search_default_group_by_state': 1}</field>
            <field name="search_view_id" ref="hr_contract_view_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new contract
              </p>
            </field>
        </record>

        <menuitem
            id="menu_human_resources_configuration_contract"
            name="Contracts"
            parent="hr.menu_human_resources_configuration"
            sequence="25"/>

        <menuitem
            id="hr_menu_contract"
            name="Contracts"
            action="hr_contract.action_hr_contract"
            parent="hr.menu_hr_employee_payroll"
            sequence="6"
            groups="hr_contract.group_hr_contract_manager"/>

        <menuitem
            id="menu_hr_employee_contracts"
            name="Contracts"
            action="hr_contract.action_hr_contract"
            parent="hr.menu_hr_root"
            sequence="5"/>

        <record id="hr.menu_resource_calendar_view" model="ir.ui.menu">
            <field name="parent_id" ref="menu_human_resources_configuration_contract"/>
        </record>
</odoo>
