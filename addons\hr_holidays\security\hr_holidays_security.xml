<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.module.category" id="base.module_category_human_resources_time_off">
        <field name="description">A user without any rights on Time Off will be able to see the application, create his own holidays and manage the requests of the users he's manager of.</field>
        <field name="sequence">10</field>
    </record>

    <record id="group_hr_holidays_responsible" model="res.groups">
        <field name="name">Time Off Responsible</field>
        <field name="category_id" ref="base.module_category_hidden"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="group_hr_holidays_user" model="res.groups">
        <field name="name">Officer: Manage all requests</field>
        <field name="category_id" ref="base.module_category_human_resources_time_off"/>
        <field name="implied_ids" eval="[(4, ref('hr_holidays.group_hr_holidays_responsible')), (4, ref('hr.group_hr_user'))]"/>
    </record>

    <record id="group_hr_holidays_manager" model="res.groups">
        <field name="name">Administrator</field>
        <field name="category_id" ref="base.module_category_human_resources_time_off"/>
        <field name="implied_ids" eval="[(4, ref('hr_holidays.group_hr_holidays_user'))]"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

    <data noupdate="1">

    <record id="base.default_user" model="res.users">
        <field name="groups_id" eval="[(4,ref('hr_holidays.group_hr_holidays_manager'))]"/>
    </record>

    <record id="hr_leave_rule_employee" model="ir.rule">
        <field name="name">Time Off base.group_user read</field>
        <field name="model_id" ref="model_hr_leave"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
        <field name="perm_create" eval="False"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4,ref('base.group_user'))]"/>
    </record>

    <record id="hr_leave_rule_employee_update" model="ir.rule">
        <field name="name">Time Off base.group_user create/write</field>
        <field name="model_id" ref="model_hr_leave"/>
        <field name="domain_force">[
            '|',
                '&amp;',
                    ('employee_id.user_id', '=', user.id),
                    ('state', 'not in', ['validate', 'validate1']),
                '&amp;',
                    ('validation_type', 'in', ['manager', 'both', 'no_validation']),
                    ('employee_id.leave_manager_id', '=', user.id),
        ]</field>
        <field name="perm_read" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4,ref('base.group_user'))]"/>
    </record>

    <record id="hr_leave_rule_employee_unlink" model="ir.rule">
        <field name="name">Time Off base.group_user unlink</field>
        <field name="model_id" ref="model_hr_leave"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id), ('state', 'in', ['confirm', 'validate1'])]</field>
        <field name="perm_read" eval="False"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="True"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="hr_leave_rule_responsible_read" model="ir.rule">
        <field name="name">Time Off Responsible read</field>
        <field name="model_id" ref="model_hr_leave"/>
        <field name="domain_force">[
                ('employee_id.leave_manager_id', '=', user.id),
        ]</field>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4, ref('hr_holidays.group_hr_holidays_responsible'))]"/>
    </record>

    <record id="hr_leave_rule_responsible_update" model="ir.rule">
        <field name="name">Time Off Responsible create/write</field>
        <field name="model_id" ref="model_hr_leave"/>
        <field name="domain_force">[
            '|',
                '&amp;',
                    ('employee_id.user_id', '=', user.id),
                    ('state', '!=', 'validate'),
                ('employee_id.leave_manager_id', '=', user.id),
        ]</field>
        <field name="perm_read" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4, ref('hr_holidays.group_hr_holidays_responsible'))]"/>
    </record>

    <record id="hr_leave_rule_user_read" model="ir.rule">
        <field name="name">Time Off All Approver read</field>
        <field name="model_id" ref="model_hr_leave"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
        <field name="groups" eval="[(4, ref('hr_holidays.group_hr_holidays_user'))]"/>
    </record>

    <record id="hr_leave_rule_officer_update" model="ir.rule">
        <field name="name">Time Off All Approver create/write</field>
        <field name="model_id" ref="model_hr_leave"/>
        <field name="domain_force">[
            '|',
                '&amp;',
                    ('employee_id.user_id', '=', user.id),
                    ('state', '!=', 'validate'),
                '|',
                    ('employee_id.user_id', '!=', user.id),
                    ('employee_id.user_id', '=', False)
        ]</field>
        <field name="perm_read" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4, ref('hr_holidays.group_hr_holidays_user'))]"/>
    </record>

    <record id="hr_leave_rule_manager" model="ir.rule">
        <field name="name">Time Off Administrator</field>
        <field name="model_id" ref="model_hr_leave"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_hr_holidays_manager'))]"/>
    </record>

    <record id="hr_leave_rule_multicompany" model="ir.rule">
        <field name="name">Time Off: multi company global rule</field>
        <field name="model_id" ref="model_hr_leave"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

    <record id="hr_leave_allocation_rule_multicompany" model="ir.rule">
        <field name="name">Time Off: multi company global rule</field>
        <field name="model_id" ref="model_hr_leave_allocation"/>
        <field name="domain_force">[
            '|',
                ('employee_id', '=', False),
                ('employee_id.company_id', 'in', company_ids),
            ('holiday_status_id.company_id', 'in', company_ids + [False])
        ]</field>
    </record>

    <record id="hr_leave_allocation_rule_employee" model="ir.rule">
        <field name="name">Allocations: employee: read own</field>
        <field name="model_id" ref="model_hr_leave_allocation"/>
        <field name="domain_force">[
            '|',
                ('employee_id.leave_manager_id', '=', user.id),
                ('employee_id.user_id', '=', user.id),
        ]</field>
        <field name="perm_create" eval="False"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4,ref('base.group_user'))]"/>
    </record>

    <record id="hr_leave_allocation_rule_employee_update" model="ir.rule">
        <field name="name">Allocations: base.group_user create/write</field>
        <field name="model_id" ref="model_hr_leave_allocation"/>
        <field name="domain_force">[
            '|',
                '&amp;',
                    ('employee_id.user_id', '=', user.id),
                    ('state', '=', 'confirm'),
                '&amp;',
                    ('validation_type', 'in', ['manager', 'both', 'no_validation']),
                    ('employee_id.leave_manager_id', '=', user.id),
        ]</field>
        <field name="perm_read" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4,ref('base.group_user'))]"/>
    </record>

    <record id="hr_leave_allocation_rule_officer_read" model="ir.rule">
        <field name="name">Allocations: see all time off: read all</field>
        <field name="model_id" ref="model_hr_leave_allocation"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="perm_create" eval="False"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4, ref('hr_holidays.group_hr_holidays_user'))]"/>
    </record>

    <record id="hr_leav_allocation_rule_employee_unlink" model="ir.rule">
        <field name="name">Allocations base.group_user unlink</field>
        <field name="model_id" ref="model_hr_leave_allocation"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id), ('state', '=', 'draft')]</field>
        <field name="perm_read" eval="False"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="hr_leave_allocation_rule_officer_update" model="ir.rule">
        <field name="name">Allocations: holiday user: create/write</field>
        <field name="model_id" ref="model_hr_leave_allocation"/>
        <field name="domain_force">[
            '|',
                '&amp;',
                    ('employee_id.user_id', '=', user.id),
                    ('state', '!=', 'validate'),
                '|',
                    ('employee_id.user_id', '!=', user.id),
                    ('employee_id.user_id', '=', False)
        ]</field>
        <field name="groups" eval="[(4,ref('hr_holidays.group_hr_holidays_user'))]"/>
    </record>

    <record id="hr_leave_allocation_rule_manager" model="ir.rule">
        <field name="name">Allocations: administrator: no limit</field>
        <field name="model_id" ref="model_hr_leave_allocation"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_hr_holidays_manager'))]"/>
    </record>

    <record id="resource_leaves_base_user" model="ir.rule">
        <field name="name">Time Off Resources: Approver</field>
        <field name="model_id" ref="model_resource_calendar_leaves"/>
        <field name="domain_force">[(1,'=',1)]</field>
        <field name="perm_create" eval="False"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <!-- FIXME RLi Maybe this should be restricted somewhat -->
    <record id="resource_leaves_holidays_user" model="ir.rule">
        <field name="name">Time Off Resources: All Approver</field>
        <field name="model_id" ref="model_resource_calendar_leaves"/>
        <field name="domain_force">[(1,'=',1)]</field>
        <field name="groups" eval="[(4, ref('hr_holidays.group_hr_holidays_user'))]"/>
    </record>

    <record id="hr_holidays_status_rule_multi_company" model="ir.rule">
        <field name="name">Time Off multi company rule</field>
        <field name="model_id" ref="model_hr_leave_type"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="hr_leave_accrual_plan_rule_multi_company" model="ir.rule">
        <field name="name">Accrual plan multi company rule</field>
        <field name="model_id" ref="model_hr_leave_accrual_plan"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="hr_leave_mandatory_day_rule_multi_company" model="ir.rule">
        <field name="name">Mandatory Day: multi company rule</field>
        <field name="model_id" ref="model_hr_leave_mandatory_day"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="hr_leave_report_calendar_rule_multi_company" model="ir.rule">
        <field name="name">Time Off Report Calendar: multi company global rule</field>
        <field name="model_id" ref="model_hr_leave_report_calendar"/>
        <field name="global" eval="True"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="hr_leave_report_rule_multi_company" model="ir.rule">
        <field name="name">Time Off Report: multi company global rule</field>
        <field name="model_id" ref="model_hr_leave_report"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="hr_leave_report_rule_group_user" model="ir.rule">
        <field name="name">Time Off Summary / Report: Internal User</field>
        <field name="model_id" ref="model_hr_leave_report"/>
        <field name="domain_force">[('has_department_manager_access', '=', True)]</field>
        <field name="perm_create" eval="False"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="hr_leave_report_rule_group_holiday_user" model="ir.rule">
        <field name="name">Time Off Summary / Report: All Approver</field>
        <field name="model_id" ref="model_hr_leave_report"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="perm_create" eval="False"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4, ref('hr_holidays.group_hr_holidays_user'))]"/>
    </record>

    </data>
</odoo>
