# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:02+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>iam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "$100.00"
msgstr "$100.00"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "$120.00"
msgstr "$120.00"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "$500.00"
msgstr "$500.00"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "$600.00"
msgstr "$600.00"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "%(date_from)s - %(date_to)s"
msgstr "%(date_from)s - %(date_to)s"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "%(employee_name)s: %(expense_name)s"
msgstr "%(employee_name)s: %(expense_name)s"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid ""
"%(user)s confirms this expense is not a duplicate with similar expense."
msgstr "%(user)s ยืนยันว่าค่าใช้จ่ายนี้ไม่ซ้ำกับค่าใช้จ่ายที่เหมือนกัน"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "%s: It is not from your department"
msgstr "%s: ไม่ได้มาจากแผนกของคุณ"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "%s: It is your own expense"
msgstr "%s: เป็นค่าใช้จ่ายของคุณเอง"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "%s: Your are not a Manager or HR Officer"
msgstr "%s: คุณไม่ใช่ผู้จัดการหรือเจ้าหน้าที่ฝ่ายบุคคล"

#. module: hr_expense
#: model:ir.actions.report,print_report_name:hr_expense.action_report_hr_expense_sheet
msgid ""
"'Expenses - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"''))"
msgstr ""
"'รายจ่าย - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"''))"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "(incl"
msgstr "(รวม"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "1 %(exp_cur)s = %(rate)s %(comp_cur)s"
msgstr "1 %(exp_cur)s = %(rate)s %(comp_cur)s"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "2023-08-11"
msgstr "2023-08-11"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "<b>Wasting time recording your receipts?</b> Let’s try a better way."
msgstr "<b>เสียเวลาบันทึกใบเสร็จรับเงินของคุณ?</b> ลองวิธีที่ดีกว่านี้ดู"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "<i class=\"oi oi-arrow-right\"/> Setup your alias domain"
msgstr "<i class=\"oi oi-arrow-right\"/> ตั้งค่าโดเมนชื่อของคุณ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_move_form_inherit_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_payment_form_inherit_expense
msgid "<span class=\"o_stat_text\">Expense Report</span>"
msgstr "<span class=\"o_stat_text\">รายงานค่าใช้จ่าย</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "<span class=\"o_stat_text\">Journal Entry</span>"
msgstr "<span class=\"o_stat_text\">รายการสมุดรายวัน</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "<span>@</span>"
msgstr "<span>@</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<span>Date:</span>"
msgstr "<span>วันที่:</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<span>Employee:</span>"
msgstr "<span>พนักงาน:</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<span>Manager:</span>"
msgstr "<span>ผู้จัดการ:</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<span>Paid by:</span>"
msgstr "<span>ชำระโดย:</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
msgid "<span>The total amount doesn't match the original amount.</span>"
msgstr "<span>ยอดรวมไม่ตรงกับยอดเดิม</span>"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"A default outstanding account must be defined in the settings for company-"
"paid expenses. Or specify one in the Journal for the %(method)s payment "
"method."
msgstr ""
"บัญชีค้างชำระเริ่มต้นจะต้องกำหนดไว้ในการตั้งค่าสำหรับค่าใช้จ่ายที่บริษัทชำระ"
" หรือระบุในสมุดรายวันสำหรับวิธีการชำระเงิน %(method)s"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__account_id
msgid "Account"
msgstr "บัญชี"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Accounting"
msgstr "การบัญชี"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__accounting_date
msgid "Accounting Date"
msgstr "วันที่ลงบัญชี"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction
msgid "Action Needed"
msgstr "จำเป็นต้องดำเนินการ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_ids
msgid "Activities"
msgstr "กิจกรรม"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "การตกแต่งข้อยกเว้นกิจกรรม"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_state
msgid "Activity State"
msgstr "สถานะกิจกรรม"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Activity Type Icon"
msgstr "ไอคอนประเภทกิจกรรม"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.mail_activity_type_action_config_hr_expense
#: model:ir.ui.menu,name:hr_expense.hr_expense_menu_config_activity_type
msgid "Activity Types"
msgstr "ประเภทกิจกรรม"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_manager
msgid "Administrator"
msgstr "ผู้ดูแลระบบ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Alias"
msgstr "นามแฝง"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_user
msgid "All Approver"
msgstr "ผู้อนุมัติทั้งหมด"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_all
msgid "All Expense Reports"
msgstr "รายงานค่าใช้จ่ายทั้งหมด"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all
msgid "All Reports"
msgstr "การรายงานทั้งหมด"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "All expenses in an expense report must have the same \"paid by\" criteria."
msgstr "ค่าใช้จ่ายทั้งหมดในรายงานค่าใช้จ่ายต้องมีเกณฑ์ \"ชำระโดย\" เหมือนกัน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "All payment methods allowed"
msgstr "อนุญาตวิธีการชำระเงินทั้งหมด"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__amount_residual
msgid "Amount Due"
msgstr "จำนวนที่ค้างชําระ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "An"
msgstr " "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/mixins/document_upload.js:0
msgid "An error occurred during the upload"
msgstr "เกิดข้อผิดพลาดระหว่างการอัปโหลด"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__account_id
msgid "An expense account is expected"
msgstr "คาดว่าจะมีบัญชีรายจ่าย"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/expense_form_view.js:0
msgid "An expense of same category, amount and date already exists."
msgstr "มีรายจ่ายจ่ายของหมวดหมู่ จำนวนและวันที่เดียวกันอยู่แล้ว"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "An expense report must contain only lines from the same company."
msgstr "รายงานค่าใช้จ่ายต้องมีรายการจากบริษัทเดียวกันเท่านั้น"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_analytic_account
msgid "Analytic Account"
msgstr "บัญชีวิเคราะห์"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_distribution
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__analytic_distribution
msgid "Analytic Distribution"
msgstr "การกระจายการวิเคราะห์"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "การประยุกต์ใช้แผนการวิเคราะห์"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_precision
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__analytic_precision
msgid "Analytic Precision"
msgstr "ความแม่นยำในการวิเคราะห์"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__approval_date
msgid "Approval Date"
msgstr "วันที่อนุมัติ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__approval_state
msgid "Approval State"
msgstr "สถานะการอนุมัติ"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Approve"
msgstr "อนุมัติ"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
msgid "Approve Report"
msgstr "อนุมัติรายงาน"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__approved
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__approval_state__approve
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__approve
#: model:mail.message.subtype,name:hr_expense.mt_expense_approved
msgid "Approved"
msgstr "อนุมัติแล้ว"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_by
msgid "Approved By"
msgstr "อนุมัติโดย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_on
msgid "Approved On"
msgstr "อนุมัติเมื่อ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Attach Receipt"
msgstr "แนบใบเสร็จ"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Attach a receipt - usually an image or a PDF file."
msgstr "แนบใบเสร็จ - โดยปกติจะเป็นรูปภาพหรือไฟล์ PDF"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_ir_attachment
msgid "Attachment"
msgstr "การแนบ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_attachment_count
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_attachment_count
msgid "Attachment Count"
msgstr "จำนวนสิ่งที่แนบมา"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet_img
msgid "Attachment Name"
msgstr "ชื่อที่แนบมาด้วย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__attachment_ids
msgid "Attachments"
msgstr "แนบ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__attachment_ids
msgid "Attachments of expenses"
msgstr "เอกสารแนบค่าใช้จ่าย"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee_base
msgid "Basic Employee"
msgstr "พนักงานทั่วไป"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__tax_ids
msgid ""
"Both price-included and price-excluded taxes will behave as price-included "
"taxes for expenses."
msgstr ""
"ทั้งภาษีที่รวมราคาและไม่รวมราคาจะทำหน้าที่เป็นภาษีที่รวมราคาสำหรับค่าใช้จ่าย"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Business Trip"
msgstr "ทริปธุรกิจ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_approve
msgid "Can Approve"
msgstr "สามารถอนุมัติ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_reset
msgid "Can Reset"
msgstr "สามารถตั้งค่าใหม่"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
msgid "Cancel"
msgstr "ยกเลิก"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__cannot_approve_reason
msgid "Cannot Approve Reason"
msgstr "ไม่สามารถอนุมัติเหตุผลได้"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__product_ids
msgid "Categories"
msgstr "หมวดหมู่"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Category"
msgstr "หมวดหมู่"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category:"
msgstr "หมวดหมู่:"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category: not found"
msgstr "หมวดหมู่: ไม่พบ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_main_attachment_checksum
msgid "Checksum/SHA1"
msgstr "ตรวจสอบ/SHA1"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_communication_product_template
msgid "Communication"
msgstr "การติดต่อสื่อสาร"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__company_id
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__company_account
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Company"
msgstr "บริษัท"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_configuration
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"การแปลงระหว่างหน่วยวัดจะเกิดขึ้นได้ก็ต่อเมื่ออยู่ในหมวดหมู่เดียวกัน "
"การแปลงจะอิงตามอัตราส่วน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_kanban_view
msgid "Cost:"
msgstr "ต้นทุน:"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Create Report"
msgstr "สร้างรายงาน"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
msgid "Create a new expense report"
msgstr "สร้างการรายงานรายจ่าย"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Create a report to submit one or more expenses to your manager."
msgstr ""
"สร้างรายงานเพื่อส่งค่าใช้จ่ายอย่างน้อยหนึ่งรายการให้กับผู้จัดการของคุณ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Create expenses from incoming emails"
msgstr "สร้างรายจ่ายจากอีเมลขาเข้า"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "Create new expenses to get statistics."
msgstr "สร้างรายจ่ายใหม่เพื่อรับสถิติ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Credit Card"
msgstr "บัตรเครดิต"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__currency_id
msgid "Currency"
msgstr "สกุลเงิน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__currency_rate
msgid "Currency Rate"
msgstr "อัตราแลกเปลี่ยน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Date"
msgstr "วันที่"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Dear"
msgstr "เรียน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__hr_expense_alias_prefix
msgid "Default Alias Name for Expenses"
msgstr "ชื่อนามแฝงเริ่มต้นสำหรับรายจ่าย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_company__expense_journal_id
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__expense_journal_id
msgid "Default Expense Journal"
msgstr "สมุดรายวันค่าใช้จ่ายเริ่มต้น"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Default accounting journal for expenses paid by employees."
msgstr "สมุดรายวันการบัญชีเริ่มต้นสำหรับค่าใช้จ่ายที่จ่ายโดยพนักงาน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Default outstanding account for expenses paid by company."
msgstr "บัญชีค้างชำระค่าเริ่มต้นสำหรับค่าใช้จ่ายที่บริษัทชำระ"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_department
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__department_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Department"
msgstr "แผนก"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__name
msgid "Description"
msgstr "คำอธิบาย"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Digitalize your receipts with OCR and Artificial Intelligence"
msgstr "แปลงใบเสร็จรับเงินของคุณให้เป็นดิจิทัลด้วย OCR และปัญญาประดิษฐ์"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__distribution_analytic_account_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "บัญชีวิเคราะห์การจัดจำหน่าย"

#. module: hr_expense
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid ""
"Do not keep your expense tickets in your pockets any longer. Just snap a "
"picture of your receipt and let Odoo digitalizes it for you. The OCR and "
"Artificial Intelligence will fill the data automatically."
msgstr ""
"อย่าเก็บตั๋วค่าใช้จ่ายไว้ในกระเป๋าของคุณอีกต่อไป เพียงถ่ายภาพใบเสร็จและให้ "
"Odoo แปลงเป็นดิจิทัลให้กับคุณ OCR และปัญญาประดิษฐ์จะกรอกข้อมูลโดยอัตโนมัติ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_move_form_inherit_expense
msgid ""
"Do you really want to invoice your own company? Remove the \"Company Name\" "
"from the partner to fix the configuration. Cancel this invoice and start "
"again."
msgstr ""
"คุณต้องการออกใบแจ้งหนี้บริษัทของคุณเอง? ให้ทำการลบ \"ชื่อบริษัท\" "
"ออกจากพาร์ทเนอร์เพื่อแก้ไขการกำหนดค่า "
"ยกเลิกใบแจ้งหนี้นี้แล้วเริ่มใหม่อีกครั้ง"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "โดเมน"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__done
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__done
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/mixins/qrcode.js:0
msgid "Download our App"
msgstr "ดาวน์โหลดแอปของเรา"

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_expense_reset
msgid "Draft"
msgstr "ร่าง"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/wizard/hr_expense_approve_duplicate.py:0
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__duplicate_expense_ids
msgid "Duplicate Expense"
msgstr "รายจ่ายซ้ำ"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__employee_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Employee"
msgstr "พนักงาน"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__own_account
msgid "Employee (to reimburse)"
msgstr "พนักงาน (การชดเชย)"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Employee Expense Journal"
msgstr "สมุดรายวันค่าใช้จ่ายพนักงาน"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_account
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_account_employee_expenses
msgid "Employee Expenses"
msgstr "รายจ่ายพนักงาน"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid ""
"Enter a name then choose a category and configure the amount of your "
"expense."
msgstr "ให้ป้อนชื่อและจากนั้นเลือกหมวดหมู่และกำหนดจำนวนค่าใช้จ่ายของคุณ"

#. module: hr_expense
#: model:account.journal,name:hr_expense.hr_expense_account_journal
#: model:ir.model,name:hr_expense.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_move_line__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__expense_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_res_users__expense_manager_id
#: model:ir.model.fields.selection,name:hr_expense.selection__account_analytic_applicability__business_domain__expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Expense"
msgstr "รายจ่าย"

#. module: hr_expense
#: model:mail.activity.type,name:hr_expense.mail_act_expense_approval
msgid "Expense Approval"
msgstr "การอนุมัติรายจ่าย"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_approve_duplicate
msgid "Expense Approve Duplicate"
msgstr "อนุมัติค่าใช้จ่ายซ้ำ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_employee_tree_inherit_expense
msgid "Expense Approver"
msgstr "การอนุมัติรายจ่าย"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_product
#: model:ir.ui.menu,name:hr_expense.menu_hr_product
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Expense Categories"
msgstr "หมวดหมู่ค่าใช้จ่าย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__date
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expense Date"
msgstr "รายจ่ายวันที่"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expense Digitalization (OCR)"
msgstr "การแปลงรายจ่ายให้เป็นดิจิทัล (OCR)"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__journal_id
msgid "Expense Journal"
msgstr "สมุดบันทึกรายจ่าย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__expense_line_ids
msgid "Expense Lines"
msgstr "รายการค่าใช้จ่าย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_editable
msgid "Expense Lines Are Editable By Current User"
msgstr "บรรทัดค่าใช้จ่ายสามารถแก้ไขได้โดยผู้ใช้ปัจจุบัน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_public__expense_manager_id
msgid "Expense Manager"
msgstr "รายจ่ายผู้จัดการ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expense Outstanding Account"
msgstr "บัญชีค่าใช้จ่ายค้างชำระ"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_refuse_wizard
msgid "Expense Refuse Reason Wizard"
msgstr "ตัวช่วยสร้างเหตุผลในการปฏิเสธรายจ่าย"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_sheet
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sheet_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Report"
msgstr "รายงานรายจ่าย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__accounting_date
msgid "Expense Report Date"
msgstr "วันที่รายงานค่าใช้จ่าย"

#. module: hr_expense
#: model:ir.actions.report,name:hr_expense.action_report_expense_sheet_img
msgid "Expense Report Image"
msgstr "รูปภาพรายงานค่าใช้จ่าย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__name
msgid "Expense Report Summary"
msgstr "สรุปการรายงานรายจ่าย"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_report
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Reports"
msgstr "รายงานรายจ่าย"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_filtered
msgid "Expense Reports Analysis"
msgstr "รายงานวิเคราะห์รายจ่าย"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_to_approve
msgid "Expense Reports to Approve"
msgstr "รายงานรายจ่ายที่จะอนุมัติ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_bank_statement_line__expense_sheet_id
#: model:ir.model.fields,field_description:hr_expense.field_account_move__expense_sheet_id
#: model:ir.model.fields,field_description:hr_expense.field_account_payment__expense_sheet_id
msgid "Expense Sheet"
msgstr "เอกสารค่าใช้จ่าย"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_split
msgid "Expense Split"
msgstr "การแบ่งค่าใช้จ่าย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__expense_split_line_ids
msgid "Expense Split Line"
msgstr "บรรทัดแยกค่าใช้จ่าย"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_split_wizard
msgid "Expense Split Wizard"
msgstr "ตัวช่วยแยกค่าใช้จ่าย"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid "Expense Validate Duplicate"
msgstr "ตรวจสอบรายจ่ายซ้ำ"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "Expense categories can be reinvoiced to your customers."
msgstr "ประเภทค่าใช้จ่ายสามารถออกใบแจ้งหนี้ใหม่ให้กับลูกค้าของคุณได้"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_move.py:0
msgid "Expense entry created from: %s"
msgstr "รายการค่าใช้จ่ายที่สร้างจาก: %s"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Expense refuse reason"
msgstr "เหตุผลการปฏิเสธค่าใช้จ่าย"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_approved
msgid "Expense report approved, entry created for accountant"
msgstr "อนุมัติรายงานค่าใช้จ่ายแล้ว สร้างรายการสำหรับนักบัญชีแล้ว"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_paid
msgid "Expense report paid"
msgstr "รายงานค่าใช้จ่ายที่ชำระแล้ว"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_refused
msgid "Expense report refused"
msgstr "รายงานค่าใช้จ่ายถูกปฏิเสธ"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_reset
msgid "Expense report reset to Draft"
msgstr "รายงานค่าใช้จ่ายรีเซ็ตเป็นฉบับร่าง"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid ""
"Expense reports regroup all the expenses incurred during a specific event."
msgstr ""
"รายงานรายจ่ายที่จัดกลุ่มรายจ่ายทั้งหมดที่เกิดขึ้นใหม่ระหว่างอีเวนต์เฉพาะ"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "Expense split"
msgstr "แยกค่าใช้จ่าย"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#: model:ir.model.fields,field_description:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,field_description:hr_expense.field_product_template__can_be_expensed
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_root
#: model:product.template,name:hr_expense.product_product_no_cost_product_template
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.product_template_search_view_inherit_hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Expenses"
msgstr "รายจ่าย"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_all_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_pivot
msgid "Expenses Analysis"
msgstr "การวิเคราห์รายจ่าย"

#. module: hr_expense
#: model:ir.actions.report,name:hr_expense.action_report_hr_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Expenses Report"
msgstr "รายงานรายจ่าย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_department__expense_sheets_to_approve_count
msgid "Expenses Reports to Approve"
msgstr "รายงานรายจ่ายที่จะอนุมัติ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Expenses by Date"
msgstr "รายจ่ายตามวัน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expenses of Your Team Member"
msgstr "รายจ่ายของสมาชิกในทีมของคุณ"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "Expenses with a similar receipt to %(other_expense_name)s"
msgstr "ค่าใช้จ่ายที่มีใบเสร็จคล้ายกับ %(other_expense_name)s"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee__filter_for_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_base__filter_for_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_public__filter_for_expense
msgid "Filter For Expense"
msgstr "กรองค่าใช้จ่าย"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Flight Ticket"
msgstr "ตั๋วเครื่องบิน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_follower_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_partner_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ไอคอนแบบฟอนต์ที่ยอดเยี่ยมเช่น fa-tasks"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Former Employees"
msgstr "อดีตพนักงาน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Future Activities"
msgstr "กิจกรรมในอนาคต"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "Generate Expenses"
msgstr "สร้างค่าใช้จ่าย"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_gift_product_template
msgid "Gifts"
msgstr "ของขวัญ"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_gift_product_template
msgid "Gifts to customers or vendors"
msgstr "ของขวัญให้กับลูกค้าหรือผู้ขาย"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "Go to settings"
msgstr "ไปที่การตั้งค่า"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Guideline"
msgstr "แนวทาง"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_multiple_currency
msgid "Handle lines with different currencies"
msgstr "จัดการรายการด้วยสกุลเงินต่างๆ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__has_message
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_travel_accommodation_product_template
msgid "Hotel, plane ticket, taxi, etc."
msgstr "โรงแรม ตั๋วเครื่องบิน แท็กซี่ ฯลฯ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__hr_expense_alias_domain_id
msgid "Hr Expense Alias Domain"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__id
msgid "ID"
msgstr "ไอดี"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon"
msgstr "ไอคอน"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ไอคอนเพื่อระบุการยกเว้นกิจกรรม"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "In Payment"
msgstr "ชำระเงิน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_ids
msgid "Included taxes"
msgstr "รวมภาษีแล้ว"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Incoming Emails"
msgstr "อีเมลขาเข้า"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__description
msgid "Internal Notes"
msgstr "บันทึกย่อภายใน"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "Invalid attachments!"
msgstr "สิ่งที่แนบมาไม่ถูกต้อง!"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_editable
msgid "Is Editable By Current User"
msgstr "สามารถแก้ไขได้โดยผู้ใช้ปัจจุบัน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_is_follower
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_multiple_currency
msgid "Is currency_id different from the company_currency_id"
msgstr "currency_id แตกต่างจาก company_currency_id"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__product_has_cost
msgid "Is product with non zero cost selected"
msgstr "เป็นสินค้าที่มีต้นทุนไม่ใช่ศูนย์ที่เลือก"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "It all begins here - let's go!"
msgstr "ทุกอย่างเริ่มต้นที่นี่ - ไปกันเลย!"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__employee_journal_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Journal"
msgstr "สมุดบันทึก"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__account_move_ids
msgid "Journal Entries"
msgstr "รายการบันทึกสมุดรายวัน"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move
msgid "Journal Entry"
msgstr "รายการบันทึกประจำวัน"

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_expense_entry_delete
msgid "Journal Entry Deleted"
msgstr "สมุดรายวันถูกลบแล้ว"

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_expense_entry_draft
msgid "Journal Entry Reset to Draft"
msgstr "รายการบันทึกรายวัน รีเซ็ตเป็นฉบับร่าง"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move_line
msgid "Journal Item"
msgstr "รายการบันทึก"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "Journal entries"
msgstr "รายการสมุดรายวัน"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_entry_delete
msgid "Journal entry deleted"
msgstr "สมุดรายวันถูกลบแล้ว"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_entry_draft
msgid "Journal entry reset to draft"
msgstr "รายการบันทึกประจำวันรีเซ็ตเป็นฉบับร่าง"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__label_currency_rate
msgid "Label Currency Rate"
msgstr "อัตราสกุลเงินของป้ายกำกับ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Late Activities"
msgstr "กิจกรรมล่าสุด"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__hr_expense_use_mailgateway
msgid "Let your employees record expenses by email"
msgstr "ให้พนักงานของคุณบันทึกรายจ่ายทางอีเมล"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Let's check out where you can manage all your employees expenses"
msgstr "มาดูกันว่าคุณสามารถจัดการค่าใช้จ่ายพนักงานทั้งหมดของคุณได้ที่ไหน"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Let's go back to your expenses."
msgstr "กลับไปที่ค่าใช้จ่ายของคุณกันดีกว่า"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "Lunch with customer $12.32"
msgstr "รับประทานอาหารกลางวันกับลูกค้า $12.32"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_main_attachment_id
msgid "Main Attachment"
msgstr "เอกสารหลักที่แนบมา"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__user_id
msgid "Manager"
msgstr "ผู้จัดการ"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid ""
"Managers can approve the report here, then an accountant can post the "
"accounting entries."
msgstr ""
"ผู้จัดการสามารถอนุมัติรายงานได้ที่นี่ "
"จากนั้นนักบัญชีสามารถผ่านรายการทางบัญชีได้"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Managers can inspect all expenses from here."
msgstr "ผู้จัดการสามารถตรวจสอบค่าใช้จ่ายทั้งหมดได้จากที่นี่"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_meal_product_template
msgid "Meals"
msgstr "มื้ออาหาร"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error
msgid "Message Delivery error"
msgstr "เกิดข้อผิดพลาดในการส่งข้อความ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_mileage_product_template
msgid "Mileage"
msgstr "ไมล์สะสม"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Mitchell Admin"
msgstr "แอดมิน Mitchell"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมของฉัน"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_all
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Expenses"
msgstr "รายจ่ายของฉัน"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_my_reports
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "My Reports"
msgstr "รายงานของฉัน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Team"
msgstr "ทีมของฉัน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Name"
msgstr "ชื่อ"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
msgid "New"
msgstr "ใหม่"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "New Expense Report, paid by %(paid_by)s"
msgstr "รายงานค่าใช้จ่ายใหม่ ชำระเป็น %(paid_by)s"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "New Expense Reports"
msgstr "รายงานค่าใช้จ่ายใหม่"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "ปฏิทินอีเวนต์กิจกรรมถัดไป"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมถัดไป"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_summary
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_summary
msgid "Next Activity Summary"
msgstr "สรุปกิจกรรมถัดไป"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_id
msgid "Next Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "No attachment was provided"
msgstr "ไม่มีไฟล์แนบมา"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_department_filtered
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "No data yet!"
msgstr "ยังไม่มีข้อมูล!"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "No expense categories found. Let's create one!"
msgstr "ไม่พบหมวดหมู่ค่าใช้จ่าย มาสร้างกันเถอะ!"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid "No expense report found. Let's create one!"
msgstr "ไม่พบรายงานรายจ่าย มาสร้างกัน!"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid "No expense reports found. Let's create one!"
msgstr "ไม่พบรายงานรายจ่าย มาสร้างกัน!"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "No work contact found for the employee %s, please configure one."
msgstr "ไม่พบข้อมูลติดต่อที่ทำงานสำหรับพนักงาน %s โปรดกำหนดค่าหนึ่งรายการ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Not Refused"
msgstr "ไม่ปฏิเสธ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_categories_tree_view
msgid "Note"
msgstr "โน้ต"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Notes..."
msgstr "โน้ต..."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__nb_attachment
msgid "Number of Attachments"
msgstr "จำนวนสิ่งที่แนบมา"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__nb_expense
msgid "Number of Expenses"
msgstr "จำนวนค่าใช้จ่าย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__nb_account_move
msgid "Number of Journal Entries"
msgstr "จำนวนรายการสมุดรายวัน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "จำนวนข้อความที่ต้องดำเนินการ"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/components/expense_dashboard.xml:0
msgid "Numbers computed from your personal expenses."
msgstr "ตัวเลขคำนวณจากค่าใช้จ่ายส่วนตัวของคุณ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Odoo"
msgstr "Odoo"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid ""
"Once you have created your expense, submit it to your manager who will "
"validate it."
msgstr "เมื่อคุณสร้างค่าใช้จ่ายแล้ว ให้ส่งให้ผู้จัดการของคุณเพื่อตรวจสอบ"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid ""
"Once your <b>Expense Report</b> is ready, you can submit it to your manager "
"and wait for approval."
msgstr ""
"เมื่อ<b>รายงานค่าใช้จ่าย</b>ของคุณพร้อมแล้ว "
"คุณสามารถส่งไปยังผู้จัดการของคุณและรอการอนุมัติได้"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "Only HR Officers or the concerned employee can reset to draft."
msgstr ""
"เฉพาะเจ้าหน้าที่ฝ่ายทรัพยากรบุคคลหรือพนักงานที่เกี่ยวข้องเท่านั้นที่สามารถรีเซ็ตเป็นร่างได้"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Open bugger menu."
msgstr "เปิดเมนูบักเกอร์"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
msgid "Original Amount"
msgstr "จำนวนเงินเดิม"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_company__expense_outstanding_account_id
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__expense_outstanding_account_id
msgid "Outstanding Account"
msgstr "บัญชีคงค้าง"

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_expense_paid
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Paid"
msgstr "ชำระแล้ว"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__payment_mode
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_mode
msgid "Paid By"
msgstr "ชำระโดย"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Partial"
msgstr "บางส่วน"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
#: model:ir.model,name:hr_expense.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Pay"
msgstr "จ่าย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_method_line_id
msgid "Payment Method"
msgstr "วิธีการชำระเงิน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_state
msgid "Payment Status"
msgstr "สถานะการชำระเงิน"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_payment.py:0
msgid "Payment created for: %s"
msgstr "การชำระเงินที่สร้างขึ้นสำหรับ: %s"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Payment method allowed for expenses paid by company."
msgstr "วิธีการชำระเงินที่อนุญาตสำหรับค่าใช้จ่ายที่บริษัทชำระ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Payment methods"
msgstr "วิธีการชำระเงิน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_company__company_expense_allowed_payment_method_line_ids
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__company_expense_allowed_payment_method_line_ids
msgid "Payment methods available for expenses paid by company"
msgstr "มีวิธีการชำระเงินสำหรับค่าใช้จ่ายที่บริษัทชำระ"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_payment
msgid "Payments"
msgstr "การชำระเงิน"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_communication_product_template
msgid "Phone bills, postage, etc."
msgstr "ค่าโทรศัพท์ ค่าไปรษณีย์ ฯลฯ"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"Please specify an expense journal in order to generate accounting entries."
msgstr "โปรดระบุสมุดรายวันค่าใช้จ่ายเพื่อสร้างรายการบัญชี"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"Please specify if the expenses for this report were paid by the company, or "
"the employee"
msgstr "โปรดระบุว่าค่าใช้จ่ายสำหรับรายงานนี้ชำระโดยบริษัทหรือโดยพนักงาน"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
msgid "Post Entries"
msgstr "รายการลงบันทึก"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Post Journal Entries"
msgstr "ลงบันทึกรายวัน"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__post
msgid "Posted"
msgstr "โพสต์"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Powered by"
msgstr "สนับสนุนโดย"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Price:"
msgstr "ราคา:"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_template
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__product_id
msgid "Product"
msgstr "สินค้า"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_description
msgid "Product Description"
msgstr "รายละเอียดสินค้า"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_has_cost
msgid "Product Has Cost"
msgstr "สินค้ามีต้นทุน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Product Name"
msgstr "ชื่อสินค้า"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_product
msgid "Product Variant"
msgstr "ตัวเลือกสินค้า"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_tree_view
msgid "Product Variants"
msgstr "ตัวแปรสินค้า"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee_public
msgid "Public Employee"
msgstr "ข้าราชการ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__quantity
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Quantity"
msgstr "ปริมาณ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__rating_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__rating_ids
msgid "Ratings"
msgstr "การให้คะแนน"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid ""
"Ready? You can save it manually or discard modifications from here. You "
"don't <em>need to save</em> - Odoo will save eveyrthing for you when you "
"navigate."
msgstr ""
"พร้อมหรือยัง? คุณสามารถบันทึกด้วยตนเองหรือยกเลิกการแก้ไขได้จากที่นี่ "
"คุณไม่จำเป็นต้อง<em>บันทึกเอง</em> Odoo จะบันทึกทุกอย่างให้คุณเมื่อคุณนำทาง"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__reason
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Reason"
msgstr "เหตุผล"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Reason:"
msgstr "เหตุผล:"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_categories_tree_view
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Reference"
msgstr "การอ้างอิง"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Refund employees via their payslips."
msgstr "คืนเงินให้กับพนักงานโดยผ่านทางสลิปเงินเดือน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Refuse"
msgstr "ปฏิเสธ"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_refuse_wizard_action
msgid "Refuse Expense"
msgstr "ปฏิเสธรายจ่าย"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__refused
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__approval_state__cancel
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__cancel
#: model:mail.message.subtype,name:hr_expense.mt_expense_refused
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused"
msgstr "ปฏิเสธ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused Expenses"
msgstr "ปฏิเสธรายจ่ายแล้ว"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_payroll_expense
msgid "Reimburse Expenses in Payslip"
msgstr "ชำระรายจ่ายคืนในสลิปเงินเดือน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse in Payslip"
msgstr "ชำระคืนในสลิปเงินเดือน"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_ir_actions_report
msgid "Report Action"
msgstr "การดําเนินการรายงาน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__company_currency_id
msgid "Report Company Currency"
msgstr "รายงานสกุลเงินของบริษัท"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_reports
msgid "Reporting"
msgstr "การรายงาน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Reset to Draft"
msgstr "รีเซ็ตเป็นฉบับร่าง"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_user_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_user_id
msgid "Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบ"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_meal_product_template
msgid "Restaurants, business lunches, etc."
msgstr "ร้านอาหาร อาหารกลางวันเพื่อการติดต่อธุรกิจ ฯลฯ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_categories_tree_view
msgid "Sales Price"
msgstr "ราคาขาย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__same_receipt_expense_ids
msgid "Same Receipt Expense"
msgstr "ค่าใช้จ่ายใบเสร็จรับเงินเดียวกัน"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
msgid "Scan"
msgstr "สแกน"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/components/qrcode_action.xml:0
msgid "Scan this QR code to get the Odoo app:"
msgstr "สแกนโค้ด QR นี้เพื่อดาวน์โหลดแอป Odoo:"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,help:hr_expense.field_res_users__expense_manager_id
msgid ""
"Select the user responsible for approving \"Expenses\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""
"เลือกผู้ใช้ที่รับผิดชอบในการอนุมัติ \"ค่าใช้จ่าย\" ของพนักงานคนนี้\n"
"หากว่างเปล่า การอนุมัติจะทำโดยผู้ดูแลระบบหรือผู้อนุมัติ (กำหนดในการตั้งค่า/ผู้ใช้)"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__selectable_payment_method_line_ids
msgid "Selectable Payment Method Line"
msgstr "รายการวิธีการชำระเงินที่เลือกได้"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"Send an email to this email alias with the receipt in attachment to create "
"an expense in one click. If the first word of the mail subject contains the "
"category's internal reference or the category name, the corresponding "
"category will automatically be set. Type the expense amount in the mail "
"subject to set it on the expense too."
msgstr ""
"ส่งอีเมลไปยังอีเมลแทนพร้อมใบเสร็จในไฟล์แนบเพื่อสร้างค่าใช้จ่ายได้ในคลิกเดียว"
" หากคำแรกของหัวเรื่องอีเมลมีการอ้างอิงภายในของหมวดหมู่หรือชื่อหมวดหมู่ "
"หมวดหมู่ที่เกี่ยวข้องจะถูกตั้งค่าโดยอัตโนมัติ "
"พิมพ์จำนวนเงินค่าใช้จ่ายในจดหมายเพื่อกำหนดค่าใช้จ่ายด้วย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_expense_extract
msgid "Send bills to OCR to generate expenses"
msgstr "ส่งบิลไปที่ OCR เพื่อสร้างรายจ่าย"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_configuration
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_global_settings
msgid "Settings"
msgstr "การตั้งค่า"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__sheet_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__sheet_ids
msgid "Sheet"
msgstr "ชีต"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_bank_statement_line__show_commercial_partner_warning
#: model:ir.model.fields,field_description:hr_expense.field_account_move__show_commercial_partner_warning
msgid "Show Commercial Partner Warning"
msgstr "แสดงคำเตือนสำหรับพาร์ทเนอร์ทางการค้า"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Show all records which has next action date is before today"
msgstr "แสดงระเบียนทั้งหมดที่มีวันที่ดำเนินการถัดไปคือก่อนวันนี้"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "Show missing work email employees"
msgstr "แสดงอีเมลพนักงานที่ขาดหายไป"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__accounting_date
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__accounting_date
msgid "Specify the bill date of the related vendor bill."
msgstr "ระบุวันที่เรียกเก็บเงินของใบเรียกเก็บเงินของผู้จำหน่ายที่เกี่ยวข้อง"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,help:hr_expense.field_product_template__can_be_expensed
msgid "Specify whether the product can be selected in an expense."
msgstr "ระบุว่าสามารถเลือกสินค้าเป็นรายจ่ายได้หรือไม่"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Split Expense"
msgstr "แบ่งค่าใช้จ่าย"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/wizard/hr_expense_split_wizard.py:0
msgid "Split Expenses"
msgstr "แบ่งค่าใช้จ่าย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__split_possible
msgid "Split Possible"
msgstr "แบ่งได้"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_product_product__standard_price_update_warning
msgid "Standard Price Update Warning"
msgstr "คำเตือนการอัปเดตราคามาตรฐาน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__state
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Status"
msgstr "สถานะ"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"สถานะตามกิจกรรม\n"
"เกินกำหนด: วันที่ครบกำหนดผ่านไปแล้ว\n"
"วันนี้: วันที่จัดกิจกรรมคือวันนี้\n"
"วางแผน: กิจกรรมในอนาคต"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
msgid "Submit"
msgstr "ส่ง"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Submit to Manager"
msgstr "ส่งไปยังผู้จัดการ"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__submitted
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__approval_state__submit
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__submit
msgid "Submitted"
msgstr "ส่งแล้ว"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Subtotal"
msgstr "ยอดรวมย่อย"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Subtotal In Currency"
msgstr "ผลรวมย่อยในสกุลเงิน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Subtotal in currency"
msgstr "ผลรวมย่อยในสกุลเงิน"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_tax
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__tax_ids
msgid "Tax"
msgstr "ภาษี"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Tax 15%"
msgstr "ภาษี 15%"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_amount
msgid "Tax amount"
msgstr "จำนวนภาษี"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_amount_currency
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__tax_amount_currency
msgid "Tax amount in Currency"
msgstr "จำนวนภาษีในสกุลเงิน"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__tax_amount
msgid "Tax amount in company currency"
msgstr "จำนวนภาษีในสกุลเงินของบริษัท"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__tax_amount_currency
msgid "Tax amount in currency"
msgstr "จำนวนภาษีในสกุลเงิน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__total_tax_amount
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__tax_amount_currency
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Taxes"
msgstr "ภาษี"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_team_approver
msgid "Team Approver"
msgstr "ผู้อนุมัติทีม"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_res_company__expense_outstanding_account_id
#: model:ir.model.fields,help:hr_expense.field_res_config_settings__expense_outstanding_account_id
msgid ""
"The account used to record the outstanding amount of the company expenses."
msgstr "บัญชีที่ใช้บันทึกยอดคงค้างค่าใช้จ่ายของบริษัท"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_res_company__expense_journal_id
#: model:ir.model.fields,help:hr_expense.field_res_config_settings__expense_journal_id
msgid ""
"The company's default journal used when an employee expense is created."
msgstr "สมุดรายวันเริ่มต้นของบริษัทที่ใช้เมื่อมีการสร้างค่าใช้จ่ายของพนักงาน"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "The current user has no related employee. Please, create one."
msgstr "ผู้ใช้ปัจจุบันไม่มีพนักงานที่เกี่ยวข้อง โปรดสร้าง"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid ""
"The first word of the email subject did not correspond to any category code."
" You'll have to set the category manually on the expense."
msgstr ""
"คำแรกของหัวเรื่องอีเมลไม่ตรงกับรหัสหมวดหมู่ใดๆ "
"คุณจะต้องตั้งค่าหมวดหมู่ด้วยตนเองเกี่ยวกับรายจ่าย"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid ""
"The following approved expenses have similar employee, amount and category "
"than some expenses of this report. Please verify this report does not "
"contain duplicates."
msgstr ""
"รายจ่ายที่ได้รับอนุมัติต่อไปนี้มีพนักงาน จำนวนเงิน "
"และหมวดกมู่ที่เหมือนกันมากกว่าค่าใช้จ่ายบางรายการในรายงานนี้ "
"โปรดตรวจสอบว่ารายงานนี้ไม่มีรายการที่ซ้ำกัน"

#. module: hr_expense
#: model:ir.model.constraint,message:hr_expense.constraint_hr_expense_sheet_journal_id_required_posted
msgid "The journal must be set on posted expense"
msgstr "สมุดรายวันจะต้องตั้งค่าเป็นรายจ่ายที่ลงบันทึก"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__employee_journal_id
msgid "The journal used when the expense is paid by employee."
msgstr "สมุดรายวันที่ใช้เมื่อมีการชำระค่าใช้จ่ายโดยพนักงาน"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__payment_method_line_id
msgid "The payment method used when the expense is paid by the company."
msgstr "วิธีการชำระเงินที่ใช้เมื่อรายจ่ายถูกชำระโดยบริษัท"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "The status of all your current expenses is visible from here."
msgstr "สถานะค่าใช้จ่ายปัจจุบันทั้งหมดของคุณสามารถดูได้จากที่นี่"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_split_wizard__split_possible
msgid "The sum of after split shut remain the same"
msgstr "ผลรวมของหลังจากปิดการแยกยังคงเท่าเดิม"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"The work email of some employees is missing. Please add it on the employee "
"form"
msgstr "อีเมลที่ทำงานของพนักงานบางคนหายไป กรุณาเพิ่มลงในแบบฟอร์มพนักงาน"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/product_product.py:0
msgid ""
"There are unsubmitted expenses linked to this category. Updating the "
"category cost will change expense amounts. Make sure it is what you want to "
"do."
msgstr ""
"มีค่าใช้จ่ายที่ยังไม่ได้ส่งที่เชื่อมโยงกับหมวดหมู่นี้ "
"การอัปเดตต้นทุนหมวดหมู่จะเปลี่ยนจำนวนค่าใช้จ่าย "
"ตรวจสอบให้แน่ใจว่าเป็นสิ่งที่คุณต้องการดำเนินการ"

#. module: hr_expense
#: model_terms:web_tour.tour,rainbow_man_message:hr_expense.hr_expense_tour
msgid "There you go - expense management in a nutshell!"
msgstr "และนี่คือการจัดการค่าใช้จ่ายโดยสรุป!"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"This expense report is empty. You cannot submit or approve an empty expense "
"report."
msgstr ""
"รายงานค่าใช้จ่ายนี้ว่างเปล่า "
"คุณไม่สามารถส่งหรืออนุมัติรายงานค่าใช้จ่ายที่ว่างเปล่าได้"

#. module: hr_expense
#: model:digest.tip,name:hr_expense.digest_tip_hr_expense_0
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid "Tip: Snap pictures of your receipts with the remote app"
msgstr "เคล็ดลับ: ถ่ายภาพใบเสร็จของคุณด้วยแอปทางไกล"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "Tip: try sending receipts by email"
msgstr "เคล็ดลับ: ลองส่งใบเสร็จทางอีเมล"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__draft
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "To Report"
msgstr "ที่จะรายงาน"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__reported
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__draft
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "To Submit"
msgstr "การส่ง"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "To be Reimbursed"
msgstr "ที่จะได้รับเงินคืน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Today Activities"
msgstr "กิจกรรมวันนี้"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__total_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Total"
msgstr "รวม"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__total_amount_currency
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Total Amount"
msgstr "ยอดรวม"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount_currency
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__total_amount_currency
msgid "Total In Currency"
msgstr "รวมเป็นสกุลเงิน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
msgid "Total Taxes"
msgstr "ภาษีทั้งหมด"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__untaxed_amount_currency
msgid "Total Untaxed Amount In Currency"
msgstr "จำนวนเงินที่ไม่ต้องเสียภาษีทั้งหมดเป็นสกุลเงิน"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_split_wizard__total_amount_currency_original
msgid "Total amount of the original Expense that we are splitting"
msgstr "ยอดรวมของค่าใช้จ่ายเดิมที่เราแบ่งออก"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__total_amount_currency_original
msgid "Total amount original"
msgstr "รวมจำนวนเงินเดิม"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_travel_accommodation_product_template
msgid "Travel & Accommodation"
msgstr "การเดินทางและที่พัก"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "ประเภทกิจกรรมข้อยกเว้นบนบันทึก"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Under Validation"
msgstr "อยู่ระหว่างการตรวจสอบ"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__price_unit
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Unit Price"
msgstr "ราคาต่อหน่วย"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_id
msgid "Unit of Measure"
msgstr "หน่วยวัด"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__untaxed_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Untaxed Amount"
msgstr "ราคาก่อนรวมภาษีมูลค่าเพิ่ม"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_category_id
msgid "UoM Category"
msgstr "หมวดหมู่ UoM"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
msgid "Upload"
msgstr "อัปโหลด"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid "Upload or drop an expense receipt"
msgstr "อัปโหลดหรือวางใบเสร็จรับเงินค่าใช้จ่าย"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Use the breadcrumbs to go back to the list of expenses."
msgstr "ใช้เบรดครัมบ์เพื่อกลับไปยังรายการค่าใช้จ่าย"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_users
msgid "User"
msgstr "ผู้ใช้"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_approve_duplicate_action
msgid "Validate Duplicate Expenses"
msgstr "ตรวจสอบรายจ่ายที่ซ้ำกัน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__vendor_id
msgid "Vendor"
msgstr "ผู้ขาย"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "View Attachments"
msgstr "ดูสิ่งที่แนบมา"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "View Expense"
msgstr "ดูรายจ่าย"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "View Report"
msgstr "ดูรายงาน"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website Messages"
msgstr "ข้อความเว็บไซต์"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารของเว็บไซต์"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"When the cost of an expense product is different than 0, then the user"
"                                             using this product won't be "
"able to change the amount of the expense,"
"                                             only the quantity. Use a cost "
"different than 0 for expense categories funded by"
"                                             the company at fixed cost like "
"allowances for mileage, per diem, accommodation"
"                                             or meal."
msgstr ""
"เมื่อต้นทุนของผลิตภัณฑ์ค่าใช้จ่ายแตกต่างจาก 0 "
"ผู้ใช้ที่ใช้ผลิตภัณฑ์นี้จะไม่สามารถเปลี่ยนจำนวนค่าใช้จ่ายได้ "
"แต่จะสามารถเปลี่ยนเฉพาะปริมาณได้ ใช้ค่าใช้จ่ายที่แตกต่างจาก 0 "
"สำหรับหมวดหมู่ค่าใช้จ่ายที่ได้รับทุนจากบริษัทโดยมีต้นทุนคงที่ เช่น "
"ค่าเบี้ยเลี้ยงระยะทาง เบี้ยเลี้ยง ที่พัก หรือมื้ออาหาร"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_has_tax
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__product_has_tax
msgid "Whether tax is defined on a selected product"
msgstr "มีการกำหนดภาษีสำหรับผลิตภัณฑ์ที่เลือกหรือไม่"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__wizard_id
msgid "Wizard"
msgstr "ตัวช่วย"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You are not authorized to edit this expense report."
msgstr "คุณไม่ได้รับอนุญาตให้แก้ไขรายงานรายจ่ายนี้"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You are not authorized to edit this expense."
msgstr "คุณไม่ได้รับอนุญาตให้แก้ไขค่าใช้จ่ายนี้"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You can not create report without category."
msgstr "คุณไม่สามารถสร้างรายงานโดยไม่มีหมวดหมู่ได้"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "You can now submit it to the manager from the following link."
msgstr "คุณสามารถส่งให้ผู้จัดการได้จากลิงก์ต่อไปนี้"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "You can only generate an accounting entry for approved expense(s)."
msgstr "คุณสามารถสร้างรายการบัญชีสำหรับค่าใช้จ่ายที่ได้รับอนุมัติเท่านั้น"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "You cannot add expenses of another employee."
msgstr "คุณไม่สามารถเพิ่มรายจ่ายของพนักงานคนอื่นได้"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"You cannot approve:\n"
" %s"
msgstr ""
"คุณไม่สามารถอนุมัติ:\n"
" %s"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "You cannot cancel an expense sheet linked to a posted journal entry"
msgstr ""
"คุณไม่สามารถยกเลิกแผ่นค่าใช้จ่ายที่เชื่อมโยงกับรายการบันทึกประจำวันที่ผ่านรายการแล้วได้"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"You cannot create accounting entries for an expense report without expenses."
msgstr ""
"คุณไม่สามารถสร้างรายการทางบัญชีสำหรับรายงานค่าใช้จ่ายโดยไม่มีค่าใช้จ่ายได้"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You cannot delete a posted or approved expense."
msgstr "คุณไม่สามารถลบรายจ่ายที่ลงรายการบัญชีหรืออนุมัติแล้ว"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "You cannot delete a posted or paid expense."
msgstr "คุณไม่สามารถลบค่าใช้จ่ายที่ลงบันทึกหรือชำระแล้วได้"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/analytic.py:0
msgid "You cannot delete an analytic account that is used in an expense."
msgstr "คุณไม่สามารถลบบัญชีการวิเคราะห์ที่ใช้ในค่าใช้จ่ายได้"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_move.py:0
msgid ""
"You cannot delete only some entries linked to an expense report. All entries"
" must be deleted at the same time."
msgstr ""
"คุณไม่สามารถลบเพียงบางรายการที่เชื่อมโยงกับรายงานค่าใช้จ่ายได้ "
"รายการทั้งหมดจะต้องถูกลบพร้อมกัน"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_payment.py:0
msgid ""
"You cannot delete only some payments linked to an expense report. All "
"payments must be deleted at the same time."
msgstr ""
"คุณไม่สามารถลบการชำระเงินบางส่วนที่เชื่อมโยงกับรายงานค่าใช้จ่ายได้ "
"การชำระเงินทั้งหมดจะต้องถูกลบพร้อมกัน"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_payment.py:0
msgid ""
"You cannot do this modification since the payment is linked to an expense "
"report."
msgstr ""
"คุณไม่สามารถทำการแก้ไขนี้ได้เนื่องจากการชำระเงินเชื่อมโยงกับรายงานค่าใช้จ่าย"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"You cannot refuse:\n"
" %s"
msgstr ""
"คุณไม่สามารถปฏิเสธได้:\n"
" %s"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"You cannot remove all expenses from a submitted, approved or paid expense "
"report."
msgstr ""
"คุณไม่สามารถลบค่าใช้จ่ายทั้งหมดออกจากรายงานค่าใช้จ่ายที่ส่ง "
"ได้รับการอนุมัติหรือชำระเงินแล้ว"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You cannot report expenses for different companies in the same report."
msgstr "คุณไม่สามารถรายงานค่าใช้จ่ายของบริษัทต่างๆ ในรายงานเดียวกันได้"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You cannot report expenses for different employees in the same report."
msgstr ""
"คุณไม่สามารถรายงานรายจ่ายสำหรับพนักงานที่แตกต่างกันในรายงานฉบับเดียวกันได้"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You cannot report the expenses without amount!"
msgstr "คุณไม่สามารถรายงานค่าใช้จ่ายโดยไม่มีจำนวนเงินได้!"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You cannot report twice the same line!"
msgstr "คุณไม่สามารถรายงานสองครั้งในรายการเดียวกัน!"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid ""
"You cannot set the expense total in currency to 0 if it's linked to a "
"report."
msgstr ""
"คุณไม่สามารถตั้งค่าผลรวมค่าใช้จ่ายในสกุลเงินเป็น 0 ได้หากเชื่อมโยงกับรายงาน"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You cannot set the expense total to 0 if it's linked to a report."
msgstr "คุณไม่สามารถตั้งค่าผลรวมค่าใช้จ่ายเป็น 0 ได้หากเชื่อมโยงกับรายงาน"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"You do not have the rights to add or remove any expenses on an approved or "
"paid expense report."
msgstr ""
"คุณไม่มีสิทธิ์ในการเพิ่มหรือลบค่าใช้จ่ายในรายงานค่าใช้จ่ายที่ได้รับอนุมัติหรือชำระแล้ว"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You have no expense to report"
msgstr "คุณไม่มีค่าใช้จ่ายในการรายงาน"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You need to add a manual payment method on the journal (%s)"
msgstr "คุณต้องเพิ่มวิธีการชำระเงินด้วยตนเองในสมุดรายวัน (%s)"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid ""
"You need to have at least one category that can be expensed in your database"
" to proceed!"
msgstr ""
"คุณต้องมีอย่างน้อยหนึ่งหมวดหมู่ที่สามารถใช้จ่ายในฐานข้อมูลของคุณเพื่อดำเนินการต่อ!"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Your Expense Report"
msgstr "รายงานค่าใช้จ่ายของคุณ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Your expense has been successfully registered."
msgstr "รายจ่ายของคุณได้รับการลงทะเบียนเรียบร้อยแล้ว"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "alias"
msgstr "นามแฝง"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "e.g. Lunch"
msgstr "เช่น อาหารกลางวัน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "e.g. Lunch with Customer"
msgstr "เช่น การรับประทานอาหารกลางวันกับลูกค้า"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "e.g. Restaurants: only week days, for lunch"
msgstr "เช่น ร้านอาหาร: เฉพาะวันธรรมดาสำหรับมื้อกลางวัน"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "e.g. Trip to NY"
msgstr "เช่น ทริปไป NY"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "e.g. mycompany.com"
msgstr "เช่น mycompany.com"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "expense"
msgstr "บัญชีรายจ่าย"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "has been refused"
msgstr "ถูกปฏิเสธแล้ว"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "no taxes"
msgstr "ไม่มีภาษี"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "per"
msgstr "ต่อ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "tax)"
msgstr "ภาษี)"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "to be reimbursed"
msgstr "ที่จะคืนเงิน"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "to submit"
msgstr "เพื่อส่ง"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "under validation"
msgstr "อยู่ระหว่างการตรวจสอบ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "use OCR to fill data from a picture of the bill"
msgstr "ใช้ OCR เพื่อกรอกข้อมูลจากรูปบิล"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "with the same receipt already exists."
msgstr "โดยมีใบเสร็จเดียวกันนี้อยู่แล้ว"
