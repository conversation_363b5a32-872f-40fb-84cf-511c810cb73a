# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_portal
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_totp_portal
#. odoo-javascript
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
msgid " Copy"
msgstr "Sao chép"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "(Disable two-factor authentication)"
msgstr "(Tắt xác thực hai yếu tố)"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid ""
"<i class=\"fa fa-warning\"/>\n"
"                        Two-factor authentication not enabled"
msgstr ""
"<i class=\"fa fa-warning\"/>\n"
"                        Không bật xác thực hai yếu tố"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"
msgstr "<i title=\"Tài liệu\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid ""
"<span class=\"text-success\">\n"
"                        <i class=\"fa fa-check-circle\"/>\n"
"                        Two-factor authentication enabled\n"
"                    </span>"
msgstr ""
"<span class=\"text-success\">\n"
"                        <i class=\"fa fa-check-circle\"/>\n"
"                        Xác thực hai yếu tố đang bật\n"
"                    </span>"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<strong>Added On</strong>"
msgstr "<strong>Đã thêm vào</strong>"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<strong>Trusted Device</strong>"
msgstr "<strong>Thiết bị đáng tin cậy</strong>"

#. module: auth_totp_portal
#. odoo-javascript
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
msgid "Activate"
msgstr "Kích hoạt"

#. module: auth_totp_portal
#. odoo-javascript
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
msgid "Copied!"
msgstr "Đã sao chép!"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Enable two-factor authentication"
msgstr "Bật xác thực hai yếu tố"

#. module: auth_totp_portal
#. odoo-javascript
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
msgid "Operation failed for unknown reason."
msgstr "Thao tác không thành công mà không rõ lý do."

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Revoke All"
msgstr "Thu hồi tất cả"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Two-factor authentication"
msgstr "Xác thực hai yếu tố"

#. module: auth_totp_portal
#: model:ir.model,name:auth_totp_portal.model_res_users
msgid "User"
msgstr "Người dùng"
