# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_fleet
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_log_services_view_form
msgid ""
"<span class=\"o_stat_text text-success\" invisible=\"account_move_state != 'posted'\" title=\"Service's Bill\">Service's Bill</span>\n"
"                            <span class=\"o_stat_text text-warning\" invisible=\"account_move_state == 'posted'\" title=\"Service's Bill\">Service's Bill</span>"
msgstr ""
"<span class=\"o_stat_text text-success\" invisible=\"account_move_state != 'posted'\" title=\"Service's Bill\">Рахунок від постачальника за послуги</span>\n"
"                            <span class=\"o_stat_text text-warning\" invisible=\"account_move_state == 'posted'\" title=\"Service's Bill\">Рахунок від постачальника за послуги</span>"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__account_move_ids
msgid "Account Move"
msgstr "Бухгалтерське проведення"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__account_move_line_id
msgid "Account Move Line"
msgstr "Рядок проведення"

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/fleet_vehicle_log_services.py:0
msgid "Bill"
msgstr "Рахунок"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "Bills"
msgstr "Рахунки"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__bill_count
msgid "Bills Count"
msgstr "Підрахунок рахунків"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__amount
msgid "Cost"
msgstr "Собівартість"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_automatic_entry_wizard
msgid "Create Automatic Entries"
msgstr "Створити автоматичні записи"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.account_move_view_tree
msgid "Creation Date"
msgstr "Дата створення"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move
msgid "Journal Entry"
msgstr "Запис у журналі"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move_line
msgid "Journal Item"
msgstr "Елемент журналу"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__need_vehicle
msgid "Need Vehicle"
msgstr "Потрібен транспортний засіб"

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/account_move.py:0
msgid "Service Vendor Bill: %s"
msgstr "Рахунок постачальника на послуги: %s"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "Послуги для транспорту"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__account_move_state
msgid "Status"
msgstr "Статус"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__vehicle_id
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__vehicle_id
msgid "Vehicle"
msgstr "Транспортний засіб"

#. module: account_fleet
#: model:fleet.service.type,name:account_fleet.data_fleet_service_type_vendor_bill
msgid "Vendor Bill"
msgstr "Рахунок від постачальника"

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/fleet_vehicle_log_services.py:0
msgid ""
"You cannot delete log services records because one or more of them were bill"
" created."
msgstr ""
"Ви не можете видалити записи про послуги журналу, оскільки один або кілька з"
" них були створені."

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/fleet_vehicle_log_services.py:0
msgid ""
"You cannot modify amount of services linked to an account move line. Do it "
"on the related accounting entry instead."
msgstr ""
"Ви не можете змінити кількість послуг, пов’язаних із рядком проводки. "
"Натомість зробіть це у відповідній бухгалтерській проводці."

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "show the vendor bills for this vehicle"
msgstr "показати рахунки постачальника для цього транспортного засобу"
