<odoo>
    <template id="portal_my_home_overdue_invoice" name="Portal layout: overdue invoices" inherit_id="portal.portal_breadcrumbs" priority="30">
        <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
            <t t-if="page_name == 'overdue_invoices'">
                <li t-attf-class="breadcrumb-item active">
                    <a t-attf-href="/my/invoices?{{ keep_query() }}">Invoices &amp; Bills</a>
                </li>
                <li class="breadcrumb-item active">
                    <t t-out="payment['reference']"/>
                </li>
            </t>
        </xpath>

        <xpath expr="//ol[hasclass('o_portal_submenu')]" position="after">
            <a t-if="page_name == 'invoice' and not invoice and filterby != 'bills' and overdue_invoice_count > 0"
                class='btn btn-primary'
                href='/my/invoices/overdue'>
                Pay overdue
            </a>
        </xpath>
    </template>

    <template id="account_payment.portal_docs_entry" inherit_id="portal.portal_docs_entry">
        <xpath expr="//a" position="inside">
            <button
                t-if="show_pay_overdue_invoices_button"
                onclick="event.preventDefault(); window.location.href='/my/invoices/overdue'"
                class="btn btn-secondary ms-auto">
                Pay Now
            </button>
        </xpath>
    </template>

    <template id="portal_my_home_account_payment" name="Overdue invoices" customize_show="True" inherit_id="portal.portal_my_home" priority="20">
        <xpath expr="//div[hasclass('o_portal_docs')]" position="before">
            <t t-set="portal_alert_category_enable" t-value="True"/>
        </xpath>
        <div id="portal_alert_category" position="inside">
            <t t-call="portal.portal_docs_entry">
                <t t-set="bg_color" t-value="'alert alert-primary align-items-center'"/>
                <t t-set="placeholder_count" t-value="'overdue_invoice_count'"/>
                <t t-set="title">Invoices to pay</t>
                <t t-set="url" t-value="'/my/invoices?filterby=overdue_invoices'"/>
                <t t-set="show_count" t-value="True"/>
                <t t-set="show_pay_overdue_invoices_button" t-value="True"/>
            </t>
        </div>
    </template>

    <template id="portal_overdue_invoices_page" name="Overdue payments">
        <t t-call="portal.portal_layout">
            <div class="row justify-content-center my-3">
                <div class="col-lg-7">
                    <div class="text-bg-light row mx-0 rounded">
                        <t t-call="payment.summary_item">
                            <t t-set="name" t-value="'amount'"/>
                            <t t-set="label">Amount</t>
                            <t t-set="value" t-value="abs(payment['amount'])"/>
                            <t t-set="options"
                                t-value="{'widget': 'monetary', 'display_currency': currency}"/>
                        </t>
                        <t t-call="payment.summary_item">
                            <t t-set="name" t-value="'reference'"/>
                            <t t-set="label">Reference</t>
                            <t t-set="value" t-value="payment['reference']"/>
                            <t t-set="include_separator" t-value="True"/>
                        </t>
                    </div>
                    <div class="mt-4">
                        <t t-call="payment.form"/>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <template id="portal_my_invoices_payment" name="Payment on My Invoices" inherit_id="account.portal_my_invoices">
        <xpath expr="//t[@t-call='portal.portal_table']/thead/tr/th[last()]" position="after">
            <th class="d-none d-lg-table-cell"/>
            <th/>
        </xpath>
        <xpath expr="//t[@t-foreach='invoices']/tr/td[last()]" position="after">
            <td class="d-none d-lg-table-cell text-center">
                <t t-if="invoice_data.get('installment_state', False) in ('next', 'overdue')">
                    <span
                        t-attf-class="{{'text-danger' if invoice_data['installment_state'] == 'overdue' else ''}}"
                        t-out="invoice_data['next_amount_to_pay']"
                        t-options="{'widget': 'monetary', 'display_currency': invoice.currency_id}"
                    />
                    <small
                        t-if="invoice_data['installment_state'] == 'next'"
                        class="o_portal_invoice_due_date"
                        t-att-datetime="invoice_data['next_due_date']"
                    />
                    <small t-if="invoice_data['installment_state'] == 'overdue'" class="text-danger">
                        overdue
                    </small>
                </t>
            </td>
            <td class="text-center">
                <a t-if="invoice._has_to_be_paid()"
                    t-att-href="invoice.get_portal_url(anchor='portal_pay', query_string='&amp;payment=True')"
                    title="Pay Now"
                    aria-label="Pay now"
                    class="btn btn-sm btn-primary"
                    role="button"
                >
                    <i class="fa fa-arrow-circle-right"/><span class='d-none d-md-inline'> Pay Now</span>
                </a>
            </td>
        </xpath>
        <xpath expr="//t[@name='invoice_status_posted']/span[1]" position="before">
            <t t-set="tx_sudo" t-value="invoice.get_portal_last_transaction()"/>
        </xpath>
        <xpath expr="//span[@name='invoice_status_waiting_for_payment']" position="before">
            <span t-elif="invoice.payment_state in ('not_paid', 'partial') and tx_sudo.state == 'authorized'"
                    class="badge rounded-pill text-bg-primary">
                <i class="fa fa-fw fa-check"/>
                <span class="d-none d-md-inline"> Authorized</span>
            </span>
            <span t-elif="invoice.payment_state in ('not_paid', 'partial') and tx_sudo.state == 'pending' and tx_sudo.provider_code not in ('none', 'custom')"
                    class="badge rounded-pill text-bg-warning">
                <span class="d-none d-md-inline"> Pending</span>
            </span>
            <span t-elif="invoice.payment_state in ('paid', 'in_payment') and tx_sudo.state == 'done'" class="badge rounded-pill text-bg-success">
                <i class="fa fa-fw fa-check"></i>
                <span class="d-none d-md-inline"> Paid</span>
            </span>
        </xpath>
    </template>

    <template id="portal_invoice_payment" name="Invoice Payment">
        <div class="row">
            <div class="modal fade" id="pay_with" role="dialog">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header pb-0">
                            <h3 class="modal-title">Pay</h3>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div t-if="company_mismatch">
                                <t t-call="payment.company_mismatch_warning"/>
                            </div>
                            <div t-elif="installment_state in ('next', 'overdue') and amount_due != next_amount_to_pay" id="o_payment_installments_modal">
                                <div class="btn-group w-100" id="o_payment_tabs" role="tablist">
                                    <button class="btn btn-outline-primary o_btn_payment_tab active"
                                        id="o_payment_installments_tab"
                                        data-bs-toggle="pill"
                                        data-bs-target="#o_payment_installments"
                                        type="button"
                                        role="tab"
                                        aria-controls="o_payment_installments"
                                        aria-selected="true"
                                    >
                                        <strong>Installment</strong>
                                        <br/>
                                        <t
                                            t-out="next_amount_to_pay"
                                            t-options="{'widget': 'monetary', 'display_currency': currency}"
                                        />
                                    </button>
                                    <button class="btn btn-outline-primary o_btn_payment_tab"
                                        id="o_payment_full_tab"
                                        data-bs-toggle="pill"
                                        data-bs-target="#o_payment_full"
                                        type="button"
                                        role="tab"
                                        aria-controls="o_payment_full"
                                        aria-selected="false"
                                    >
                                        <strong>Full Amount</strong><br/>
                                        <t t-out="amount_due" t-options="{'widget': 'monetary', 'display_currency': currency}"/>
                                    </button>
                                </div>
                                <div class="tab-content" id="o_payment_tabcontent">
                                    <div t-if="payment_state == 'in_payment'" class="alert alert-danger mt-2">
                                        A payment has already been made on this invoice, please make sure to not pay twice.
                                    </div>
                                    <div
                                        class="tab-pane my-3 show active"
                                        id="o_payment_installments"
                                        role="tabpanel"
                                        aria-labelledby="o_payment_installments_tab"
                                        tabindex="0"
                                    >
                                        <div class="text-bg-light row row-cols-1 row-cols-md-2 mx-0 py-2 rounded">
                                            <t t-call="payment.summary_item">
                                                <t t-set="name" t-value="'amount_installment'"/>
                                                <t t-set="label">Amount</t>
                                                <t t-set="value" t-value="next_amount_to_pay"/>
                                                <t t-set="options"
                                                    t-value="{'widget': 'monetary', 'display_currency': currency}"
                                                />
                                            </t>
                                            <t t-call="payment.summary_item">
                                                <t t-set="name" t-value="'reference_installment'"/>
                                                <t t-set="label">Reference</t>
                                                <t t-set="value" t-value="next_payment_reference"/>
                                                <t t-set="include_separator" t-value="True"/>
                                            </t>
                                        </div>
                                    </div>
                                    <div class="tab-pane my-3"
                                        id="o_payment_full"
                                        role="tabpanel"
                                        aria-labelledby="o_payment_full_tab"
                                        tabindex="0"
                                    >
                                        <div t-if="show_epd" class="alert alert-success">
                                            Early Payment Discount of <span t-out="epd_discount_amount_currency" t-options="{'widget': 'monetary', 'display_currency': currency}"/> has been applied.
                                        </div>
                                        <div class="text-bg-light row row-cols-1 row-cols-md-2 mx-0 py-2 rounded">
                                            <t t-call="payment.summary_item">
                                                <t t-set="name" t-value="'amount_full'"/>
                                                <t t-set="label">Amount</t>
                                                <t t-set="value" t-value="amount_due"/>
                                                <t t-set="options"
                                                    t-value="{'widget': 'monetary', 'display_currency': currency}"
                                                />
                                            </t>
                                            <t t-call="payment.summary_item">
                                                <t t-set="name" t-value="'reference_full'"/>
                                                <t t-set="label">Reference</t>
                                                <t t-set="value" t-value="invoice_name"/>
                                                <t t-set="include_separator" t-value="True"/>
                                            </t>
                                        </div>
                                    </div>
                                    <!-- set default amount as the max. amount due, the 'amount'
                                         will be updated/set js-side depending the active tab -->
                                    <t t-call="account_payment.form">
                                        <t t-set="amount" t-value="amount_due"/>
                                    </t>
                                </div>
                            </div>
                            <t t-else="">
                                <div t-if="show_epd" class="alert alert-success">
                                    Early Payment Discount of <span t-out="epd_discount_amount_currency" t-options="{'widget': 'monetary', 'display_currency': currency}"/> has been applied.
                                </div>
                                <div t-if="payment_state == 'in_payment'" class="alert alert-danger">
                                    A payment has already been made on this invoice, please make sure to not pay twice.
                                </div>
                                <div class="text-bg-light row row-cols-1 row-cols-md-2 mx-0 mb-3 py-2 rounded">
                                    <t t-call="payment.summary_item">
                                        <t t-set="name" t-value="'amount_full'"/>
                                        <t t-set="label">Amount</t>
                                        <t t-set="value" t-value="amount_due"/>
                                        <t t-set="options"
                                            t-value="{'widget': 'monetary', 'display_currency': currency}"
                                        />
                                    </t>
                                    <t t-call="payment.summary_item">
                                        <t t-set="name" t-value="'reference_full'"/>
                                        <t t-set="label">Reference</t>
                                        <t t-set="value" t-value="next_payment_reference"/>
                                        <t t-set="include_separator" t-value="True"/>
                                    </t>
                                </div>
                                <t t-call="account_payment.form">
                                    <t t-set="amount" t-value="amount_due"/>
                                </t>
                            </t>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <template id="portal_invoice_payment_paid" name="Invoice Paid">
        <div class="modal fade" id="pay_with" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">Pay Invoice</h3>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning text-center" role="alert" t-out="invoice._get_online_payment_error()"/>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <template id="portal_invoice_page_inherit_payment" name="Payment on My Invoices" inherit_id="account.portal_invoice_page">
        <xpath expr="//t[@t-call='portal.portal_record_sidebar']//h2[1]" position="after">
            <t t-set="pending_txs" t-value="invoice.transaction_ids.filtered(lambda tx: tx.state in ('pending', 'authorized') and tx.provider_code not in ('none', 'custom'))"/>
            <div t-if="invoice.currency_id.is_zero(invoice.amount_residual)" class="rounded text-bg-success fw-normal badge">
                <i class="fa fa-fw fa-check-circle"/> Paid
            </div>
            <div t-if="invoice.payment_state == 'in_payment' and not invoice.currency_id.is_zero(invoice.amount_residual)" class="rounded text-bg-info fw-normal badge">
                <i class="fa fa-fw fa-check-circle"/> Processing Payment
            </div>
            <div t-elif="pending_txs" class="rounded text-bg-info fw-normal badge">
                <i class="fa fa-fw fa-check-circle"/> Pending
            </div>
        </xpath>
        <xpath expr="//t[@t-call='portal.portal_record_sidebar']//div[hasclass('o_download_pdf')]" position="before">
            <a t-if="invoice._has_to_be_paid()"
                href="#"
                class="btn btn-primary"
                data-bs-toggle="modal"
                data-bs-target="#pay_with">
                <i class="fa fa-fw fa-arrow-circle-right"/> Pay Now
            </a>
        </xpath>
        <xpath expr="//div[@id='invoice_content']//div[hasclass('o_portal_html_view')]" position="before">
            <t t-set="tx" t-value="invoice.get_portal_last_transaction()"/>
            <div t-if="invoice.get_portal_last_transaction() and invoice.amount_total and not success and not error and (invoice.payment_state != 'not_paid' or tx.state in ('pending', 'authorized'))"
                 class="o_account_payment_tx_status"
                 t-att-data-invoice-id="invoice.id">
                <t t-call="payment.state_header"/>
            </div>
            <div t-if="invoice._has_to_be_paid()" id="portal_pay" t-att-data-payment="payment">
                <t t-call="account_payment.portal_invoice_payment"/>
            </div>
            <div t-else="" id="portal_pay" t-att-data-payment="payment">
                <t t-call="account_payment.portal_invoice_payment_paid"/>
            </div>
        </xpath>
    </template>

    <template id="portal_invoice_error" name="Invoice error display: payment errors"
            inherit_id="account.portal_invoice_error">
        <xpath expr="//t[@name='generic']" position="after">
            <t t-if="error == 'pay_invoice_invalid_doc'">
                There was an error processing your payment: invalid invoice.
            </t>
            <t t-elif="error == 'pay_invoice_invalid_token'">
                There was en error processing your payment: invalid credit card ID.
            </t>
            <t t-elif="error == 'pay_invoice_tx_fail'">
                There was an error processing your payment: transaction failed.<br />
                <t t-set="tx_sudo" t-value="invoice.get_portal_last_transaction()"/>
                <t t-if="tx_sudo and tx_sudo.state_message">
                    <t t-out="tx_sudo.state_message"/>
                </t>
            </t>
            <t t-elif="error == 'pay_invoice_tx_token'">
                There was an error processing your payment: issue with credit card ID validation.
            </t>
        </xpath>
    </template>

    <template id="portal_invoice_success" name="Invoice success display: payment success"
            inherit_id="account.portal_invoice_success">
        <xpath expr="//a[hasclass('close')]" position="after">
            <t t-if="success == 'pay_invoice'">
                <t t-set="tx_sudo" t-value="invoice.get_portal_last_transaction()"/>
                <span t-if="tx_sudo.provider_id.sudo().done_msg" t-out="tx_sudo.provider_id.sudo().done_msg"/>
                <div t-if="tx_sudo.provider_id.sudo().pending_msg and tx_sudo.provider_code == 'custom' and invoice.ref">
                    <b>Communication: </b><span t-out='invoice.ref'/>
                </div>
            </t>
            <t t-if="success == 'pay_invoice' and invoice.payment_state in ('paid', 'in_payment')">
                Done, your online payment has been successfully processed. Thank you for your order.
            </t>
        </xpath>
    </template>
</odoo>
