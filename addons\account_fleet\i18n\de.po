# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_fleet
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_log_services_view_form
msgid ""
"<span class=\"o_stat_text text-success\" invisible=\"account_move_state != 'posted'\" title=\"Service's Bill\">Service's Bill</span>\n"
"                            <span class=\"o_stat_text text-warning\" invisible=\"account_move_state == 'posted'\" title=\"Service's Bill\">Service's Bill</span>"
msgstr ""
"<span class=\"o_stat_text text-success\" invisible=\"account_move_state != 'posted'\" title=\"Service's Bill\">Rechnung für Dienstleistung</span>\n"
"                            <span class=\"o_stat_text text-warning\" invisible=\"account_move_state == 'posted'\" title=\"Service's Bill\">Rechnung für Dienstleistung</span>"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__account_move_ids
msgid "Account Move"
msgstr "Kontobuchung"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__account_move_line_id
msgid "Account Move Line"
msgstr "Kontobuchungszeile"

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/fleet_vehicle_log_services.py:0
msgid "Bill"
msgstr "Rechnung"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "Bills"
msgstr "Rechnungen"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__bill_count
msgid "Bills Count"
msgstr "Anzahl der Rechnungen"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__amount
msgid "Cost"
msgstr "Kosten"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_automatic_entry_wizard
msgid "Create Automatic Entries"
msgstr "Automatische Buchungen erstellen"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.account_move_view_tree
msgid "Creation Date"
msgstr "Erstellungsdatum"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move
msgid "Journal Entry"
msgstr "Journalbuchung"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move_line
msgid "Journal Item"
msgstr "Buchungszeile"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__need_vehicle
msgid "Need Vehicle"
msgstr "Fahrzeug benötigt"

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/account_move.py:0
msgid "Service Vendor Bill: %s"
msgstr "Lieferantenrechnungen für Dienstleistung: %s"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "Fahrzeugservices"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__account_move_state
msgid "Status"
msgstr "Status"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__vehicle_id
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__vehicle_id
msgid "Vehicle"
msgstr "Fahrzeug"

#. module: account_fleet
#: model:fleet.service.type,name:account_fleet.data_fleet_service_type_vendor_bill
msgid "Vendor Bill"
msgstr "Lieferantenrechnung"

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/fleet_vehicle_log_services.py:0
msgid ""
"You cannot delete log services records because one or more of them were bill"
" created."
msgstr ""
"Sie können keine Protokolleinträge löschen, weil einer oder mehrere von "
"ihnen per Rechnung erstellt wurden."

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/fleet_vehicle_log_services.py:0
msgid ""
"You cannot modify amount of services linked to an account move line. Do it "
"on the related accounting entry instead."
msgstr ""
"Sie können den Betrag der mit einer Kontobuchungszeile verbundenen "
"Dienstleistungen nicht ändern. Tun Sie dies stattdessen in der zugehörigen "
"Buchungszeile."

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "show the vendor bills for this vehicle"
msgstr "die Lieferantenrechnungen für dieses Fahrzeug anzeigen"
