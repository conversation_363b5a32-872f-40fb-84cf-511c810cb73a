# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_proxy_client
# 
# Translators:
# Wil <PERSON>doo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"A user already exists with theses credentials on our server. Please check "
"your information."
msgstr ""
"Un utilisateur existe déjà avec ces identifiants sur notre serveur. Veuillez"
" vérifier vos informations."

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_account_edi_proxy_client_user
msgid "Account EDI proxy user"
msgstr "Compte EDI de l'utilisateur proxy"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_res_company__account_edi_proxy_client_ids
msgid "Account Edi Proxy Client"
msgstr "Compte EDI du client proxy"

#. module: account_edi_proxy_client
#: model_terms:ir.ui.view,arch_db:account_edi_proxy_client.view_form_account_edi_proxy_client_user
msgid "Account Journal"
msgstr "Journal des comptes"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__active
msgid "Active"
msgstr "Actif"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__company_id
msgid "Company"
msgstr "Société"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__create_date
msgid "Created on"
msgstr "Créé le"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_certificate_key
msgid "Cryptographic Keys"
msgstr "Clés cryptographiques"

#. module: account_edi_proxy_client
#: model:ir.model.fields.selection,name:account_edi_proxy_client.selection__account_edi_proxy_client_user__edi_mode__demo
msgid "Demo mode"
msgstr "Mode démo"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: account_edi_proxy_client
#: model:ir.actions.act_window,name:account_edi_proxy_client.action_tree_account_edi_proxy_client_user
msgid "EDI Proxy User"
msgstr "EDI de l'utilisateur proxy"

#. module: account_edi_proxy_client
#: model:ir.ui.menu,name:account_edi_proxy_client.menu_account_proxy_client_user
msgid "EDI Proxy Users"
msgstr "EDI des utilisateurs proxy"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_mode
msgid "EDI operating mode"
msgstr "Mode de fonctionnement EDI"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_identification
msgid "Edi Identification"
msgstr "Identification Edi"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__id
msgid "ID"
msgstr "ID"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__id_client
msgid "Id Client"
msgstr "Id Client"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"Invalid signature for request. This might be due to another connection to odoo Access Point server. It can occur if you have duplicated your database. \n"
"\n"
"If you are not sure how to fix this, please contact our support."
msgstr ""
"Signature invalide pour la demande. Cela peut être dû à une autre connexion au serveur Odoo Access Point. Ceci peut se produire si vous avez dupliqué votre base de données. \n"
"\n"
"Si vous n'êtes pas sûr de la façon de résoudre ce problème, veuillez contacter notre assistance."

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key_id
msgid "Private Key"
msgstr "Clé privée"

#. module: account_edi_proxy_client
#: model:ir.model.fields.selection,name:account_edi_proxy_client.selection__account_edi_proxy_client_user__edi_mode__prod
msgid "Production mode"
msgstr "Mode de production"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__proxy_type
msgid "Proxy Type"
msgstr "Type de proxy"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__refresh_token
msgid "Refresh Token"
msgstr "Actualiser le jeton"

#. module: account_edi_proxy_client
#: model:ir.model.fields.selection,name:account_edi_proxy_client.selection__account_edi_proxy_client_user__edi_mode__test
msgid "Test mode"
msgstr "Mode test"

#. module: account_edi_proxy_client
#: model:ir.model.fields,help:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key_id
msgid "The key to encrypt all the user's data"
msgstr "La clé pour chiffrer toutes les données de l'utilisateur"

#. module: account_edi_proxy_client
#: model:ir.model.fields,help:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_identification
msgid "The unique id that identifies this user, typically the vat"
msgstr ""
"L'identifiant unique qui identifie cet utilisateur, habituellement la TVA"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"The url that this service requested returned an error. The url it tried to "
"contact was %(url)s. %(error_message)s"
msgstr ""
"L'URL demandée par ce service a renvoyé une erreur. L'URL qu'il a essayé de "
"contacter était  %(url)s. %(error_message)s"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"The url that this service requested returned an error. The url it tried to "
"contact was %s"
msgstr ""
"L'URL demandée par ce service a renvoyé une erreur. L'URL qu'il a essayé de "
"contacter était %s"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"The url that this service tried to contact does not exist. The url was “%s”"
msgstr ""
"L'URL que ce service a essayé de contacter n'existe pas. L'URL était “%s”"

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_active_company_proxy
msgid "This company has an active user already created for this EDI type"
msgstr "Cette société a déjà un utilisateur actif créé pour ce type d'EDI"

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_active_edi_identification
msgid "This edi identification is already assigned to an active user"
msgstr "Cette identification EDI est déjà assignée à un utilisateur actif"

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_id_client
msgid "This id_client is already used on another user."
msgstr "Cet id_client est déjà utilisé sur un autre utilisateur."
