# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_check_printing
# 
# Translators:
# Wil Odoo, 2025
# Sarah <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Sarah Park, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
msgid "%(journal)s: Check Number Sequence"
msgstr "%(journal)s: 수표 번호 연번"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "<span>&amp;nbsp;</span>"
msgstr "<span>&amp;nbsp;</span>"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid ""
"Adjust the margins of generated checks to make it fit your printer's "
"settings."
msgstr "프린터 설정에 맞도록 생성한 수표의 여백을 조정하세요."

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_amount_in_words
msgid "Amount in Words"
msgstr "글로 쓴 금액"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid "Bills"
msgstr "거래처 청구서"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Cancel"
msgstr "취소"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__bank_check_printing_layout
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid "Check Layout"
msgstr "수표 레이아웃"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_left
msgid "Check Left Margin"
msgstr "수표 왼쪽 여백"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_number
msgid "Check Number"
msgstr "수표 번호"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Check Printing"
msgstr "수표 인쇄하기"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_right
msgid "Check Right Margin"
msgstr "수표 오른쪽 여백"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_sequence_id
msgid "Check Sequence"
msgstr "수표 순서"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid "Check Top Margin"
msgstr "수표 위쪽 여백"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid "Check numbers can only consist of digits"
msgstr "수표 번호는 숫자로만 구성되어야 합니다"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr "수표에 번호가 없는 경우 선택하세요."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Check to print"
msgstr "수표 인쇄"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: model:account.payment.method,name:account_check_printing.account_payment_method_check
msgid "Checks"
msgstr "수표"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_sequence_id
msgid "Checks numbering sequence."
msgstr "수표 연번입니다."

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_payment_check_printing_search
msgid "Checks to Print"
msgstr "인쇄할 수표"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Checks to print"
msgstr "인쇄할 수표"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_company
msgid "Companies"
msgstr "회사"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_config_settings
msgid "Config Settings"
msgstr "환경 설정"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_uid
msgid "Created by"
msgstr "작성자"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_date
msgid "Created on"
msgstr "작성일자"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Default"
msgstr "기본"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__display_name
msgid "Display Name"
msgstr "표시명"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid "Go to the configuration panel"
msgstr "설정창으로 이동하기"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__id
msgid "ID"
msgstr "ID"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr "한 번에 여러 장의 수표를 인쇄하려면 은행 계좌가 동일해야 합니다."

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_journal
msgid "Journal"
msgstr "전표"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Manual Numbering"
msgstr "번호 수기 지정"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Providers: Each payment provider has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""
"수동 결제: Odoo 외부에서 여러 가지 수단으로 결제하거나 지급받습니다.\n"
"결제대행업체: 결제업체마다 자체 결제 수단을 지정하고 있습니다. 온라인 구매나 구독을 할 경우, 협력사에서 저장한 결제 토큰을 사용하여 카드로 결제를 요청합니다.\n"
"수표: Odoo에서 수표로 청구 금액을 지급하고 수표 인쇄도 할 수 있습니다.\n"
"일괄 입금: 고객 수표를 여러 개 수령한 후 한 번에 일괄 입금을 생성하여 은행에 입금합니다.  account_batch_payment 모듈을 설치해야 합니다.\n"
"SEPA 계좌이체: 은행으로 SEPA 계좌이체 파일을 전송하여 SEPA 지역에서 결제할 수 있습니다. account_sepa 모듈을 설치해야 합니다.\n"
"SEPA 자동이체: 협력사에서 위임한 권한을 통해 SEPA 지역에서 결제를 진행합니다. account_sepa 모듈을 설치해야 합니다.\n"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid "Multi-Pages Check Stub"
msgstr "수표 부본 (수표책)"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_next_number
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__next_check_number
msgid "Next Check Number"
msgstr "다음 수표 번호"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: code:addons/account_check_printing/wizard/print_prenumbered_checks.py:0
msgid "Next Check Number should only contains numbers."
msgstr "다음 수표 번호에는 숫자만 포함되어야 합니다."

#. module: account_check_printing
#: model:ir.model.fields.selection,name:account_check_printing.selection__res_company__account_check_printing_layout__disabled
msgid "None"
msgstr "없음"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__payment_method_line_id
msgid "Payment Method"
msgstr "결제 방법"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment_method
msgid "Payment Methods"
msgstr "지급 방법"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment
msgid "Payments"
msgstr "결제"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"Payments to print as a checks must have 'Check' selected as payment method "
"and not have already been reconciled"
msgstr "수표를 인쇄하려면 반드시 결제 방법으로 '수표'를 선택해야 합니다. 결제 항목이 조정되기 전이어야 합니다."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"Please enter the number of the first pre-printed check that you are about to"
" print on."
msgstr "인쇄하려는 첫 사전 인쇄 수표의 번호를 입력하세요."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print"
msgstr "인쇄"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Print Check"
msgstr "수표 인쇄"

#. module: account_check_printing
#: model:ir.actions.server,name:account_check_printing.action_account_print_checks
msgid "Print Checks"
msgstr "수표 인쇄"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid "Print Date Label"
msgstr "날짜 라벨 인쇄"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: model:ir.model,name:account_check_printing.model_print_prenumbered_checks
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print Pre-numbered Checks"
msgstr "번호가 사전에 매겨진 수표 인쇄"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid "Refunds"
msgstr "환불"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_right
msgid "Right Margin"
msgstr "오른쪽 여백"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid ""
"Select the format corresponding to the check paper you will be printing your checks on.\n"
"In order to disable the printing feature, select 'None'."
msgstr ""
"수표 용지에 인쇄할 양식을 선택하세요.\n"
"인쇄 기능을 사용하지 않으려면 '없음'을 선택하세요."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Sent"
msgstr "발송 완료"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_next_number
msgid "Sequence number of the next printed check."
msgstr "다음에 인쇄된 수표의 일련번호"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__show_check_number
msgid "Show Check Number"
msgstr "수표 번호 표시"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"Something went wrong with Check Layout, please select another layout in "
"Invoicing/Accounting Settings and try again."
msgstr "수표 레이아웃에 문제가 발생했습니다. 청구서/회계 설정에서 다른 레이아웃을 선택한 후 다시 시도하세요."

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"The following numbers are already used:\n"
"%s"
msgstr ""
"이미 사용 중인 번호입니다:\n"
"%s"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
msgid ""
"The last check number was %s. In order to avoid a check being rejected by "
"the bank, you can only use a greater number."
msgstr "마지막 수표 번호는 %s입니다. 은행에서 수표가 거부되지 않도록 더 큰 번호만 사용할 수 있습니다."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_number
msgid ""
"The selected journal is configured to print check numbers. If your pre-"
"printed check paper already has numbers or if the current numbering is "
"wrong, you can change it in the journal configuration page."
msgstr ""
"선택한 전표에서 수표 번호를 인쇄하도록 설정되어 있습니다. 미리 인쇄된 수표에 이미 번호가 있거나 현재 번호가 잘못되어 있으면 전표 환경"
" 설정 페이지에서 수정할 수 있습니다."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid ""
"This option allows you to print check details (stub) on multiple pages if "
"they don't fit on a single page."
msgstr "이 옵션을 선택하면 수표 세부 정보 (스텁)가 한 페이지에 들어가지 않는 경우 여러 페이지에 인쇄할 수 있습니다."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid ""
"This option allows you to print the date label on the check as per CPA.\n"
"Disable this if your pre-printed check includes the date label."
msgstr ""
"이 옵션을 사용하면 CPA별로 수표에 날짜 라벨을 인쇄할 수 있습니다.\n"
"미리 인쇄된 수표에 날짜 라벨이 포함되어 있는 경우에는 이 옵션을 비활성화하세요."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"This will allow to save on payments the number of the corresponding check."
msgstr "이렇게 하면 해당되는 수표 숫자만큼 결제 금액을 절약할 수 있습니다."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Unmark Sent"
msgstr "전송 취소 표시"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Void Check"
msgstr "무효 수표"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"You have to choose a check layout. For this, go in Invoicing/Accounting "
"Settings, search for 'Checks layout' and set one."
msgstr "수표 레이아웃을 선택하세요. 선택하려면 청구서/회계 설정에서 '수표 레이아웃'을 검색하여 설정하세요."
