# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays
# 
# Translators:
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid " to %(date_to_utc)s"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important &gt;&lt;/td&gt;"
msgstr "!important &gt;&lt;/td&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important /&gt;"
msgstr "!important /&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important/&gt;"
msgstr "!important/&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 10px\" &gt;"
msgstr "!important; font-size: 10px\" &gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 8px; min-width: 18px\"&gt;"
msgstr "!important; font-size: 8px; min-width: 18px\"&gt;"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "%(allocation_name)s (from %(date_from)s to %(date_to)s)"
msgstr "%(allocation_name)s (від %(date_from)s до %(date_to)s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "%(allocation_name)s (from %(date_from)s to No Limit)"
msgstr "%(allocation_name)s (від %(date_from)s до необмеженості)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(employee)s on Time Off : %(duration)s"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(employee_name)s - from %(date_from)s to %(date_to)s - %(state)s"
msgstr "%(employee_name)s - з %(date_from)s до %(date_to)s - %(state)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(holiday_name)s has been refused."
msgstr "%(holiday_name)s відхилено."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"%(leave_name)s has been cancelled with the justification: <br/> %(reason)s."
msgstr "%(leave_name)s скасовано з обґрунтуванням: <br/> %(reason)s."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(leave_type)s: %(duration)s (%(start)s)"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_leave_allocation_generate_multi_wizard.py:0
msgid "%(name)s (%(duration)s %(request_unit)s(s))"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "%(name)s (%(duration)s day(s))"
msgstr "%(name)s (%(duration)s день(і))"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "%(name)s (%(duration)s hour(s))"
msgstr "%(name)s (%(duration)s година(и))"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid "%(name)s (%(time)g remaining out of %(maximum)g days)"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid "%(name)s (%(time)g remaining out of %(maximum)g hours)"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(name)s: %(duration)s"
msgstr "%(name)s: %(duration)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(person)s on %(leave_type)s: %(duration)s (%(start)s)"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(person)s: %(duration)s (%(start)s)"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid "%s (copy)"
msgstr "%s (копія)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%s: Time Off"
msgstr "%s: Відпустка"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&gt;"
msgstr "&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/td&gt;"
msgstr "&lt;/td&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/th&gt;"
msgstr "&lt;/th&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"
msgstr ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;td style=background-color:"
msgstr "&lt;td style=background-color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;th class=\"text-center\" colspan="
msgstr "&lt;th class=\"text-center\" colspan="

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "(valid until"
msgstr "(дійсний до"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "- Valid for"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "00:00"
msgstr "00:00"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-check\"/> Validate"
msgstr "<i class=\"fa fa-check\"/> Затвердити"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual' or state != "
"'confirm'\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual' or state != "
"'confirm'\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual'\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual'\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "<i class=\"fa fa-long-arrow-right\" title=\"to\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"to\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-thumbs-up\"/> Approve"
msgstr "<i class=\"fa fa-thumbs-up\"/> Затвердити"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-times\"/> Refuse"
msgstr "<i class=\"fa fa-times\"/> Відхилити"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
msgid ""
"<span class=\"ml8\" invisible=\"request_unit == 'hour'\">Days</span>\n"
"                            <span class=\"ml8\" invisible=\"request_unit != 'hour'\">Hours</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<span class=\"ml8\" invisible=\"type_request_unit == 'hour'\">Days</span>\n"
"                                <span class=\"ml8\" invisible=\"type_request_unit != 'hour'\">Hours</span>"
msgstr ""
"<span class=\"ml8\" invisible=\"type_request_unit == 'hour'\">Днів</span>\n"
"                                <span class=\"ml8\" invisible=\"type_request_unit != 'hour'\">Годин</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Back On\n"
"                        </span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Time Off\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Відпустка\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                           Time Off\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                           Відпустка\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Accruals</span>"
msgstr "<span class=\"o_stat_text\">Нарахування</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Allocations</span>"
msgstr "<span class=\"o_stat_text\">Розподілення</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Time Off</span>"
msgstr "<span class=\"o_stat_text\">Відпустка</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\"> to </span>"
msgstr "<span class=\"text-muted\"> до </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\">from </span>"
msgstr "<span class=\"text-muted\">з </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span>The employee has a different timezone than yours! Here dates and times are displayed in the employee's timezone</span>\n"
"                    ("
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span>You can only take this time off in whole days, so if your schedule has"
" half days, it won't be used efficiently.</span>"
msgstr ""
"<span>Ви можете взяти цю відпустку лише цілими днями, тому якщо у вашому "
"розкладі є половина днів, вона не буде використана ефективно.</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "<strong>Departments and Employees</strong>"
msgstr "<strong>Відділення та співробітники</strong>"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "A cancelled leave cannot be modified."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr ""
"Чудовий спосіб відстежувати ВОМ працівника, дні хвороби та статус "
"затвердження."

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid ""
"A great way to keep track on your time off requests, sick days, and approval"
" status."
msgstr ""
"Чудовий спосіб відстежувати свої вихідні дні, лікарняні та статус "
"затвердження."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "A time off cannot be duplicated."
msgstr "Відпустка не може повторюватися."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__show_leaves
msgid "Able to see Remaining Time Off"
msgstr "Може бачити залишену відпустку"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid "Absence"
msgstr "Відсутність"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__absence_of_today
msgid "Absence by Today"
msgstr "Відсутність на сьогодні"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid ""
"Absent Employee(s), Whose time off requests are either confirmed or "
"validated on today"
msgstr ""
"Відсутній працівник(и), чиї запити на відпустку сьогодні або підтверджені"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_employee_action_from_department
msgid "Absent Employees"
msgstr "Відсутні співробітники"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__is_absent
msgid "Absent Today"
msgstr "Відсутній сьогодні"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Accrual (Future):"
msgstr "Нарахування (майбутнє):"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__accrual
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_type__accrual
msgid "Accrual Allocation"
msgstr "Розподіл нарахувань"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Accrual Level"
msgstr "Рівень нарахування"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_plan
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_plan_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__accrual_plan_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__accrual_plan_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Accrual Plan"
msgstr "План нарахування"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_level
msgid "Accrual Plan Level"
msgstr "Рівень плану нарахування"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
msgid "Accrual Plan's Employees"
msgstr "Співробітники плану нарахування"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_accrual_plans
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_accrual_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_tree
msgid "Accrual Plans"
msgstr "Плани нарахування"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.hr_leave_allocation_cron_accrual_ir_actions_server
msgid "Accrual Time Off: Updates the number of time off"
msgstr "Нарахування відпустки: оновлення кількості неробочих днів"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_validity
msgid "Accrual Validity"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_validity_count
msgid "Accrual Validity Count"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_validity_type
msgid "Accrual Validity Type"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accruals_ids
msgid "Accruals"
msgstr "Нарахування"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accrual_count
msgid "Accruals count"
msgstr "Підрахунок нарахувань"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrued_gain_time
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__accrued_gain_time
msgid "Accrued Gain Time"
msgstr "Накопичений час"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "Action Needed"
msgstr "Необхідна дія"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__active
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__active
msgid "Active"
msgstr "Активно"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Allocations"
msgstr "Активні нарахування"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__active_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__active_employee
msgid "Active Employee"
msgstr "Активний співробітник"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
msgid "Active but on leave"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_ids
msgid "Activities"
msgstr "Дії"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Оформлення виключення дії"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_state
msgid "Activity State"
msgstr "Статус дії"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Activity Type Icon"
msgstr "Іконка типу дії"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.mail_activity_type_action_config_hr_holidays
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_config_activity_type
msgid "Activity Types"
msgstr "Типи дії"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Add a description..."
msgstr "Додати опис..."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Add a reason..."
msgstr "Додати причину..."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Add some description for the people that will validate it"
msgstr "Додайте якийсь опис для людей, які будуть затверджувати це"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__added_value_type
msgid "Added Value Type"
msgstr "Тип доданого значення"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_manager
msgid "Administrator"
msgstr "Адміністратор"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__end_of_accrual
msgid "After this accrual's period"
msgstr "Після цього періоду нарахування"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__pm
msgid "Afternoon"
msgstr "Вдень"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_all
msgid "All Allocations"
msgstr "Усі нарахування"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_dashboard
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_action_approve_department
msgid "All Time Off"
msgstr "Уся відпустка"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__all
msgid "All accrued time carried over"
msgstr "Весь накопичений час перенесено"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__last_several_days
msgid "All day"
msgstr "Ввесь день"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Allocated ("
msgstr "Розподілено ("

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__duration_display
msgid "Allocated (Days/Hours)"
msgstr "Заброньовані (Дні/Години)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__allocation_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__duration
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Allocation"
msgstr "Бронювання"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_approval
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
msgid "Allocation Approval"
msgstr "Схвалення розподілу"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_display
msgid "Allocation Display"
msgstr "Відображення розподілу"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__allocation_mode
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__allocation_mode
msgid "Allocation Mode"
msgstr "Режим розподілу"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_notif_subtype_id
msgid "Allocation Notification Subtype"
msgstr "Підтип сповіщення про розподіл"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_remaining_display
msgid "Allocation Remaining Display"
msgstr "Відображати залишок розподілу"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/wizard/hr_leave_allocation_generate_multi_wizard.py:0
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__allocation_id
#: model:mail.message.subtype,description:hr_holidays.mt_leave_allocation
#: model:mail.message.subtype,name:hr_holidays.mt_leave_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Allocation Request"
msgstr "Запит на розподіл"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Allocation Requests"
msgstr "Запити на розподіл"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_second_approval
msgid "Allocation Second Approval"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__allocation_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__allocation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Allocation Type"
msgstr "Тип розподілу"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"Allocation must be confirmed \"To Approve\" or validated once \"Second "
"Approval\" in order to approve it."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Allocation of %(leave_type)s: %(amount).2f %(unit)s to %(target)s"
msgstr "Призначення %(leave_type)s: %(amount).2f %(unit)s до %(target)s"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__week_day
msgid "Allocation on"
msgstr "Розподілення на"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"Allocation request must be confirmed, second approval or validated in order "
"to refuse it."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Allocation state must be \"Refused\" in order to be reset to \"To Approve\"."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__allocation_to_approve_count
msgid "Allocation to Approve"
msgstr "Розподіл до затвердження"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_approve_department
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_count
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_manager_approve_allocations
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Allocations"
msgstr "Розподіл"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allows_negative
msgid "Allow Negative Cap"
msgstr "Дозволити негативне обмеження"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Allow To Attach Supporting Document"
msgstr "Дозволити прикріпляти супутні документи"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__allocation_mode
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_generate_multi_wizard__allocation_mode
msgid ""
"Allow to create requests in batchs:\n"
"- By Employee: for a specific employee\n"
"- By Company: all employees of the specified company\n"
"- By Department: all employees of the specified department\n"
"- By Employee Tag: all employees of the specific employee group category"
msgstr ""
"Дозвольте створювати запити групою:\n"
"- Співробітником: для конкретного співробітника\n"
"- Компанією: всіх працівників зазначеної компанії\n"
"- Відділом: усі співробітники зазначеного відділу\n"
"- За тегом Працівник: всі працівники конкретної категорії працівників групи"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__already_accrued
msgid "Already Accrued"
msgstr "Вже нараховано"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Amount"
msgstr "Сума"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "An employee already booked time off which overlaps with this period:%s"
msgstr "Працівник уже забронював відпустку, яка збігається з цим періодом:%s"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Analyze from"
msgstr "Аналізувати від"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_evaluation_report_graph
msgid "Appraisal Analysis"
msgstr "Аналіз атестації"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_validation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Approval"
msgstr "Схвалення"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/views/calendar/common/calendar_common_popover.xml:0
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Approve"
msgstr "Затвердити"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.ir_actions_server_approve_allocations
msgid "Approve Allocations"
msgstr "Затвердити розподіл"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__approved
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_dashboard_new_time_off
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Approved"
msgstr "Затверджено"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Approved Requests"
msgstr "Затверджені запити"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Approved:"
msgstr "Затверджено:"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__apr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__apr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__apr
msgid "April"
msgstr "Квітень"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Archived"
msgstr "Заархівовано"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
msgid "Are you sure you want to delete this record?"
msgstr "Ви впевнені, що хочете видалити цей запис?"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "Arrow"
msgstr "Стрілка"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "Arrow icon"
msgstr "Іконка стрілки"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__allocation
msgid "At the allocation date"
msgstr "На дату розподілу"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__accrued_gain_time__end
msgid "At the end of the accrual period"
msgstr "В кінці періоду нарахування"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__accrued_gain_time__start
msgid "At the start of the accrual period"
msgstr "На старт періоду розподілу"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__year_start
msgid "At the start of the year"
msgstr "На початок року"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "At work"
msgstr "На роботі"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids
msgid "Attach File"
msgstr "Прикріпити файл"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_attachment_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_attachment_count
msgid "Attachment Count"
msgstr "Підрахунок прикріплення"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__attachment_ids
msgid "Attachments"
msgstr "Прикріплення"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__aug
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__aug
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__aug
msgid "August"
msgstr "Серпень"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_leave_generate_multi_wizard.py:0
msgid ""
"Automatic time off spliting during batch generation is not managed for ovelapping time off declared in hours. Conflicting time off:\n"
"%s"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Available"
msgstr "Доступно"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__remaining_leaves
msgid "Available Time Off Days"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Available:"
msgstr "Доступний:"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
msgid "Away"
msgstr "Відійшов"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Back On"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/persona_model_patch.js:0
msgid "Back on %s"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_balance
msgid "Balance"
msgstr "Баланс"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "Balance at the"
msgstr "Баланс на"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__is_based_on_worked_time
msgid "Based on worked time"
msgstr "На основі робочого часу"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee_base
msgid "Basic Employee"
msgstr "Звичайний користувач"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__both
msgid "Both Approved and Confirmed"
msgstr "І підтверджено і затверджено"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_mode__company
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_generate_multi_wizard__allocation_mode__company
msgid "By Company"
msgstr "Компанією"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_mode__department
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_generate_multi_wizard__allocation_mode__department
msgid "By Department"
msgstr "Відділом"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_mode__employee
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_generate_multi_wizard__allocation_mode__employee
msgid "By Employee"
msgstr "По співробітникам"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_mode__category
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_generate_multi_wizard__allocation_mode__category
msgid "By Employee Tag"
msgstr "За тегом співробітника"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__manager
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__manager
msgid "By Employee's Approver"
msgstr "Затверджувачем співробітника"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__both
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__both
msgid "By Employee's Approver and Time Off Officer"
msgstr "Затверджувач працівника та менеджер відпусток"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__hr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__hr
msgid "By Time Off Officer"
msgstr "За менеджером відпусток"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_calendar_event
msgid "Calendar Event"
msgstr "Календар подій"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_approve
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__can_approve
msgid "Can Approve"
msgstr "Можна затвердити"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_cancel
msgid "Can Cancel"
msgstr "Можна скасувати"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__can_modify_value_type
msgid "Can Modify Value Type"
msgstr "Може редагувати тип значення"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_reset
msgid "Can reset"
msgstr "Можна скинути"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Cancel"
msgstr "Скасувати"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/static/src/views/hooks.js:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
msgid "Cancel Time Off"
msgstr "Скасувати відпустку"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_cancel_leave
msgid "Cancel Time Off Wizard"
msgstr "Помічник скасування відпусток"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__cancel
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Cancelled"
msgstr "Скасовано"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Cancelled Time Off"
msgstr "Скасовані відпустки"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time
msgid "Cap accrued time"
msgstr "Максимальний накопичений час"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Cap:"
msgstr "Накопичення:"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__carried_over_days_expiration_date
msgid "Carried over days expiration date"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Carry Over Validity"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__action_with_unused_accruals
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Carry over"
msgstr "Перенести"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__maximum
msgid "Carry over with a maximum"
msgstr "Перенести з максимумом"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Carry over:"
msgstr "Перенести:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Carry-Over Date"
msgstr "Дата перенесення"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_date
msgid "Carry-Over Time"
msgstr "Час перенесення"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_day
msgid "Carryover Day"
msgstr "День перенесення"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_day_display
msgid "Carryover Day Display"
msgstr "Відображення дня відображення"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_month
msgid "Carryover Month"
msgstr "Місяць перенесення"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee_base.py:0
msgid ""
"Changing this working schedule results in the affected employee(s) not "
"having enough leaves allocated to accomodate for their leaves already taken "
"in the future. Please review this employee's leaves and adjust their "
"allocation accordingly."
msgstr ""
"Зміна цього робочого графіка призводить до того, що відповідний(і) "
"працівник(и) не матиме достатньо відпусток, призначених для відпусток, які "
"вони вже взяли в майбутньому. Будь ласка, перегляньте відпустки цього "
"працівника та відповідно відкоригуйте їх розподіл."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Choose a cap for this accrual."
msgstr "Оберіть перенесення для цього нарахування."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__responsible_ids
msgid ""
"Choose the Time Off Officers who will be notified to approve allocation or "
"Time Off Request. If empty, nobody will be notified"
msgstr ""
"Виберіть співробітників, які отримають сповіщення про затвердження розподілу"
" або запит на відпустку. Якщо пусто, ніхто не буде повідомлений"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Click on any date or on this button to request a time-off"
msgstr ""
"Натисніть на будь-яку дату або на цю кнопку, щоб подати запит на відпустку"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__color
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__color
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Color"
msgstr "Колір"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__company_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Company"
msgstr "Компанія"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_comp
msgid "Compensatory Days"
msgstr "Дні компенсації"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Configuration"
msgstr "Налаштування"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
msgid "Confirmation"
msgstr "Підтвердження"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__confirmed
msgid "Confirmed"
msgstr "Підтверджено"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
msgid "Confirmed and Approved"
msgstr "Підтверджено та схвалено"

#. module: hr_holidays
#: model_terms:web_tour.tour,rainbow_man_message:hr_holidays.hr_holidays_tour
msgid "Congrats, we can see that your request has been validated."
msgstr "Вітаємо, ми бачимо, що ваш запит підтверджено."

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid ""
"Count of allocations for this time off type (approved or waiting for "
"approbation) with a validity period starting this year."
msgstr ""
"Кількість розподілів для цього типу відпустки (затверджено або очікує на "
"затвердження) з періодом дії, починаючи з цього року."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Count of plans linked to this time off type."
msgstr "Кількість планів, пов’язаних із цим типом відпустки."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid ""
"Count of time off requests for this time off type (approved or waiting for "
"approbation) with a start date in the current year."
msgstr ""
"Кількість запитів на відпустку для цього типу відпустки (схвалено або очікує"
" на затвердження) з датою початку в поточному році."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__country_id
msgid "Country"
msgstr "Країна"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__country_code
msgid "Country Code"
msgstr "Код країни"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__icon_id
msgid "Cover Image"
msgstr "Обкладинка"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
msgid "Create Allocations"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
msgid "Create a new time off allocation"
msgstr "Створити новий розподіл завершеного часу"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid "Create a new time off allocation request"
msgstr "Створити новий запит на розподіл відпустки"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_uid
msgid "Created by"
msgstr "Створив"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_date
msgid "Created on"
msgstr "Створено"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__current_leave_state
msgid "Current Time Off Status"
msgstr "Статус поточної відпустки"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_id
msgid "Current Time Off Type"
msgstr "Тип поточного завершеного часу"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Current Year"
msgstr "Поточний рік"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Currently Valid"
msgstr "Наразі дійсний"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_hours
msgid "Custom Hours"
msgstr "Кастомні години"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__daily
msgid "Daily"
msgstr "Щодня"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_new_request
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_new_request
msgid "Dashboard"
msgstr "Дашборд"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Date"
msgstr "Дата"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from_period
msgid "Date Period Start"
msgstr "Початок періоду дати"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__lastcall
msgid "Date of the last accrual allocation"
msgstr "Дата останнього розподілу нарахування"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__nextcall
msgid "Date of the next accrual allocation"
msgstr "Дата наступного розподілу нарахування"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Dates"
msgstr "Дати"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__day
msgid "Day"
msgstr "День"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__accrual_validity_type__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__added_value_type__day
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Days"
msgstr "Дні"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__dec
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__dec
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__dec
msgid "December"
msgstr "Грудень"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__max_allowed_negative
msgid ""
"Define the maximum level of negative days this kind of time off can reach. "
"Value must be at least 1."
msgstr ""
"Визначте максимальний рівень від’ємних днів, який може досягти цей вид "
"відпустки. Значення має бути не менше 1."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Delete"
msgstr "Видалити"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_department
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__department_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Department"
msgstr "Відділ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Department search"
msgstr "Пошук відділу"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__department_ids
msgid "Departments"
msgstr "Відділи"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Помічник звільнення"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__description
msgid "Description"
msgstr "Опис"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name_validity
msgid "Description with validity"
msgstr "Опис із затвердженням"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "Discard"
msgstr "Відмінити"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Display Option"
msgstr "Відображати опцію"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_calendar_meeting
msgid "Display Time Off in Calendar"
msgstr "Відображати відпустку у календарі"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
msgid ""
"Due to a change in global time offs, %s extra day(s) have been taken from "
"your allocation. Please review this leave if you need it to be changed."
msgstr ""
"У зв’язку зі зміною загального вихідного часу, %s з вашого розподілу "
"вилучено додаткові дні. Будь ласка, перегляньте цю відпустку, якщо вам "
"потрібно її змінити."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
msgid ""
"Due to a change in global time offs, this leave no longer has the required "
"amount of available allocation and has been set to refused. Please review "
"this leave."
msgstr ""
"У зв’язку зі зміною загального часу відпустки ця відпустка більше не має "
"необхідної кількості доступних ресурсів, тому їй було відмовлено. Будь "
"ласка, перегляньте цю відпустку."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
msgid ""
"Due to a change in global time offs, you have been granted %s day(s) back."
msgstr ""
"У зв’язку зі зміною загального часу відпустки вам надано %s днів на "
"повернення."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__duration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Duration"
msgstr "Тривалість"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_graph
msgid "Duration (Days)"
msgstr "Тривалість (Дні)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_graph
msgid "Duration (Hours)"
msgstr "Тривалість (Години)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days_display
msgid "Duration (days)"
msgstr "Тривалість (Дні)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid "Duration (hours)"
msgstr "Тривалість (години)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days
msgid "Duration in days. Reference field to use when necessary."
msgstr "Тривалість у днях. Довідкове поле для використання при необхідності."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
msgid "Edit Allocation"
msgstr "Редагувати бронювання"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Edit Time Off"
msgstr "Редагувати відпустку"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__employee_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Employee"
msgstr "Співробітник"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__active_employee
msgid "Employee Active"
msgstr "Співробітник активний"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_company_id
msgid "Employee Company"
msgstr "Компанія співробітника"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__employee_requests
msgid "Employee Requests"
msgstr "Запити співробітника"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__category_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__category_id
msgid "Employee Tag"
msgstr "Мітка співробітника"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Employee accrue"
msgstr "Нарахування співробітника"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban_approve_department
msgid "Employee's image"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__emp
msgid "Employee(s)"
msgstr "Співробітник(и)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__employees_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__employee_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__employee_ids
msgid "Employees"
msgstr "Співробітники"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Employees Off Today"
msgstr "У співроібтників сьогодні вихідний"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__end_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_to
msgid "End Date"
msgstr "Кінцева дата"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__yes
msgid "Extra Days Requests Allowed"
msgstr "Дозволені запитувані додаткові дні"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__employee_requests
msgid ""
"Extra Days Requests Allowed: User can request an allocation for himself.\n"
"\n"
"        Not Allowed: User cannot request an allocation."
msgstr ""
"Завтерджені запити на додаткові дні: Користувач може надсилати запит на розподілення на себе.\n"
"\n"
"        Не затерджено: Користувач не може надсилати запит на розподіл."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__feb
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__feb
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__feb
msgid "February"
msgstr "Лютий"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__duration_display
msgid ""
"Field allowing to see the allocation duration in days or hours depending on "
"the type_request_unit"
msgstr ""
"Поле дозволяє бачити бронювання тривалості у днях або годинах залежно від "
"type_request_unit"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__duration_display
msgid ""
"Field allowing to see the leave request duration in days or hours depending "
"on the leave_type_request_unit"
msgstr ""
"Поле дозволяє бачити тривалість запиту на відпустку у днях та годинах "
"залежно від leave_type_request_unit"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__first_approver_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__approver_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "First Approval"
msgstr "Перше затвердження"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day
msgid "First Day"
msgstr "Перший день"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day_display
msgid "First Day Display"
msgstr "Перший день відображення"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month
msgid "First Month"
msgstr "Перший місяць"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day
msgid "First Month Day"
msgstr "Перший день місяця"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day_display
msgid "First Month Day Display"
msgstr "Відображення першого дня місяця"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_follower_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_follower_ids
msgid "Followers"
msgstr "Підписники"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_partner_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_partner_ids
msgid "Followers (Partners)"
msgstr "Підписники (Партнери)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Іконка з чудовим шрифтом, напр. fa-tasks"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days_display
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid ""
"For an Accrual Allocation, this field contains the theorical amount of time "
"given to the employee, due to a previous start date, on the first run of the"
" plan. This can be manually edited."
msgstr ""
"Для накопичувального розподілу це поле містить теоретичну кількість часу, "
"наданого працівнику, через попередню дату початку, під час першого запуску "
"плану. Це можна редагувати вручну."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__frequency
msgid "Frequency"
msgstr "Частота"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__fri
msgid "Friday"
msgstr "П’ятниця"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__start_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "From"
msgstr "Від"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_from
msgid "From Date"
msgstr "Дата з"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Future Activities"
msgstr "Майбутні дії"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "Generate Time Off"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_allocation_generate_multi_wizard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
msgid "Generate time off allocations for multiple employees"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_generate_multi_wizard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "Generate time off for multiple employees"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_leave_allocation_generate_multi_wizard.py:0
msgid "Generated Allocations"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_leave_generate_multi_wizard.py:0
msgid "Generated Time Off"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "Grant Time"
msgstr "Наданий час"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Group By"
msgstr "Групувати за"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__group_days_leave
msgid "Group Time Off"
msgstr "Групувати відпутски"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_approval
msgid "HR Approval"
msgstr "Затвердження відділу кадрів"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_summary_employee
msgid "HR Time Off Summary Report By Employee"
msgstr "Підсумковий звіт відпусток відділу кадрів по співробітникам"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_half
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__half_day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__half_day
msgid "Half Day"
msgstr "Півдня"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__has_mandatory_day
msgid "Has Mandatory Day"
msgstr "Має обов'язковий день"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__has_message
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__has_message
msgid "Has Message"
msgstr "Є повідомлення"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "Has Valid Allocation"
msgstr "Має дійсний розподіл"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_hatched
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_hatched
msgid "Hatched"
msgstr "З'явився"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__holiday_status
msgid "Holiday Status"
msgstr "Статус вихідного"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_report_hr_holidays_report_holidayssummary
msgid "Holidays Summary Report"
msgstr "Зведений звіт про свята"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_from
msgid "Hour from"
msgstr "Година від"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_to
msgid "Hour to"
msgstr "Година до"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__hourly
msgid "Hourly"
msgstr "Погодинно"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__added_value_type__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__hour
msgid "Hours"
msgstr "Години"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__hr_icon_display
msgid "Hr Icon Display"
msgstr "Відображення іконки Hr"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__id
msgid "ID"
msgstr "ID"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon"
msgstr "Значок"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Іконка для визначення виключення дії."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
msgid "Idle"
msgstr "Не задіяний"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Якщо позначено, то нові повідомлення будуть потребувати вашої уваги."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Якщо позначено, деякі повідомлення мають помилку доставки."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__is_based_on_worked_time
msgid ""
"If checked, the accrual period will be calculated according to the work "
"days, not calendar days."
msgstr ""
"Якщо позначено, період нарахування буде розраховуватися за робочими днями, а"
" не за календарними."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__allows_negative
msgid ""
"If checked, users request can exceed the allocated days and balance can go "
"in negative."
msgstr ""
"Якщо позначено, запит користувача може перевищити виділені дні, а баланс "
"може стати від’ємним."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__active_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__active_employee
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Якщо активне поле встановлено як Помилкове, це дозволить вам приховати запис"
" кадрів, не видаливши його."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__active
msgid ""
"If the active field is set to false, it will allow you to hide the time off "
"type without removing it."
msgstr ""
"Якщо активне поле встановлене на помилкове,  це дозволить вам приховати "
"відпустку, не видаляючи її."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_duration_check
msgid ""
"If you want to change the number of days you should use the 'period' mode"
msgstr ""
"Якщо ви хочете змінити кількість днів, ви повинні використовувати режим "
"\"період\""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__immediately
msgid "Immediately"
msgstr "Безпосередньо"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Incorrect state for new allocation"
msgstr "Неправильний стан для нового розподілу"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_is_follower
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_is_follower
msgid "Is Follower"
msgstr "Стежить"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__is_name_custom
msgid "Is Name Custom"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__is_officer
msgid "Is Officer"
msgstr "Менеджер"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__unpaid
msgid "Is Unpaid"
msgstr "Неоплачено"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jan
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jan
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jan
msgid "January"
msgstr "Січень"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__job_id
msgid "Job"
msgstr "Вакансія"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Job Position"
msgstr "Посада"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__jul
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jul
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jul
msgid "July"
msgstr "Липень"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jun
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jun
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jun
msgid "June"
msgstr "Червень"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid "Keep track of your PTOs."
msgstr "Продовжуйте стежити за вашими PTO."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__time_type
msgid "Kind of Time Off"
msgstr "Тип відпустки"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Late Activities"
msgstr "Останні дії"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__leave_id
msgid "Leave"
msgstr "Відпустка"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_increases_duration
msgid "Leave Type Increases Duration"
msgstr "Тип відпустки збільшує тривалість"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__left
msgid "Left"
msgstr "Лівий"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
msgid "Legend"
msgstr "Історія"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Let's approve it"
msgstr "Давайте погодимо це"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Let's discover the Time Off application"
msgstr "Давайте відкриємо для себе модуль Відпусток"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Let's go validate it"
msgstr "Давайте підтвердимо це"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Let's try to create a Sick Time Off, select it in the list"
msgstr "Давайте спробуємо створити лікарняний, виберіть його в списку"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_count
msgid "Levels"
msgstr "Рівні"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Limit to"
msgstr "Ліміт до"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основне прикріплення"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_management
msgid "Management"
msgstr "Управління"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_manager
msgid "Manager"
msgstr "Керівник співробітника"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_manager_approval
msgid "Manager Approval"
msgstr "Затвердження менеджера"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_mandatory_day
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
msgid "Mandatory Day"
msgstr "Обов'язковий день"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_mandatory_day_action
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_mandatory_day_menu_configuration
msgid "Mandatory Days"
msgstr "Обовʼязкові дні"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__mar
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__mar
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__mar
msgid "March"
msgstr "Березень"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "Mark as ready to approve"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__max_leaves
msgid "Max Leaves"
msgstr "Максимум відпусток"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Max Time Off:"
msgstr "Максимальний завершений час:"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__max_leaves
msgid "Maximum Allowed"
msgstr "Максимально дозволено"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__max_allowed_negative
msgid "Maximum Excess Amount"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid ""
"Maximum Time Off Allowed - Time Off Already Taken - Time Off Waiting "
"Approval"
msgstr ""
"Максимальну відпустку дозволено - Відпустку повністю використано - Відпустка"
" очікує підтвердження"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__postpone_max_days
msgid "Maximum amount of accruals to transfer"
msgstr "Максимальна сума нарахувань до перерахування"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__may
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__may
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__may
msgid "May"
msgstr "Травень"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid "Meet the time off dashboard."
msgstr "Зустрічайте панель приладів відпусток."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__meeting_id
msgid "Meeting"
msgstr "Зустріч"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error
msgid "Message Delivery error"
msgstr "Помилка доставлення повідомлення"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Підтипи повідомлення"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_ids
msgid "Messages"
msgstr "Повідомлення"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_ids
msgid "Milestone"
msgstr "Віха"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid "Milestone Transition"
msgstr "Віха переходу"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time_yearly
msgid "Milestone cap"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "Mode"
msgstr "Режим"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__mon
msgid "Monday"
msgstr "Понеділок"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Month"
msgstr "Місяць"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__monthly
msgid "Monthly"
msgstr "Щомісяця"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__accrual_validity_type__month
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__month
msgid "Months"
msgstr "Місяці"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__am
msgid "Morning"
msgstr "Ранок"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/components/multi_time_off_generation_menu/multiple_time_off_generation_menu.xml:0
#: model:ir.actions.act_window,name:hr_holidays.action_hr_leave_allocation_generate_multi_wizard
#: model:ir.actions.act_window,name:hr_holidays.action_hr_leave_generate_multi_wizard
msgid "Multiple Requests"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Дедлайн моєї дії"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_my
#: model:ir.ui.menu,name:hr_holidays.menu_open_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Allocations"
msgstr "Мій розподіл"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Department"
msgstr "Мій відділ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "My Requests"
msgstr "Мої запити"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Team"
msgstr "Моя команда"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_my_leaves
msgid "My Time"
msgstr "Мій час"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_my
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "My Time Off"
msgstr "Моя відпустка"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_leaves_tree_inherit
msgid "Name"
msgstr "Назва"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Negative Cap"
msgstr "Негативне накопичення"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.xml:0
msgid "New"
msgstr "Новий"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "New %(leave_type)s Request created by %(user)s"
msgstr "Новий запит %(leave_type)s створено %(user)s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/hooks.js:0
msgid "New Allocation"
msgstr "Новий розподіл"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "New Allocation Request"
msgstr "Новий запит на розподілення"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"New Allocation Request created by %(user)s: %(count)s Days of "
"%(allocation_type)s"
msgstr ""
"Новий запит на розподіл, створений %(user)s: %(count)s днів "
"%(allocation_type)s"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "New Milestone"
msgstr "Нова віха"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
msgid "New Time Off"
msgstr "Нова відпустка"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Наступна подія календаря дій"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Дедлайн наступної дії"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_summary
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_summary
msgid "Next Activity Summary"
msgstr "Підсумок наступної дії"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_id
msgid "Next Activity Type"
msgstr "Тип наступної дії"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__no
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No Limit"
msgstr "Необмежено"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__no_validation
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__no_validation
msgid "No Validation"
msgstr "Немає перевірки"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.action_hr_available_holidays_report
#: model_terms:ir.actions.act_window,help:hr_holidays.action_hr_leave_report
#: model_terms:ir.actions.act_window,help:hr_holidays.mail_activity_type_action_config_hr_holidays
msgid "No data to display"
msgstr "Немає даних для відображення"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_report_action
msgid "No data yet!"
msgstr "Ще немає даних!"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No limit"
msgstr "Необмежено"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "No rule has been set up for this accrual plan."
msgstr "Для цього плану нарахування не було встановлено жодного правила."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Nobody will be notified"
msgstr "Ніхто не буде оповіщений"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__show_on_dashboard
msgid ""
"Non-visible allocations can still be selected when taking a leave, but will "
"simply not be displayed on the leave dashboard."
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "None"
msgstr "Немає"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__lost
msgid "None. Accrued time reset to 0"
msgstr "Жодного. Накопичений час скидається на 0"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__no
msgid "Not Allowed"
msgstr "Недозволений"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__responsible_ids
msgid "Notified Time Off Officer"
msgstr "Сповіщення керівника відпусток"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__nov
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__nov
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__nov
msgid "November"
msgstr "Листопад"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of Actions"
msgstr "Кількість дій"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_list
msgid "Number of Days"
msgstr "Кількість днів"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__number_of_hours
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__number_of_hours
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Number of Hours"
msgstr "Кількість годин"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leaves_count
msgid "Number of Time Off"
msgstr "Кількість відпусток"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_days
msgid "Number of days of the time off request. Used in the calculation."
msgstr "Кількість днів запиту на відпустку. Використовується в розрахунку."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of errors"
msgstr "Кількість помилок"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_hours
msgid "Number of hours of the time off request. Used in the calculation."
msgstr "Кількість годин запиту на відпустку. Використовується в розрахунку."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Кількість повідомлень, які вимагають дії"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Кількість повідомлень з помилковою дставкою"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__oct
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__oct
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__oct
msgid "October"
msgstr "Жовтень"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Off Today"
msgstr "Відсутній сьогодні"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_user
msgid "Officer: Manage all requests"
msgstr "Керівник: Керуйте всіма запитами"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "On Time Off"
msgstr "На відпустці"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_absent
msgid "On leave"
msgstr "У відпустці"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
msgid "Online"
msgstr "Онлайн"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Only"
msgstr "Тільки"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"Only %s's Time Off Approver, a time off Officer/Responsible or Administrator"
" can approve or refuse allocation requests."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Only a Time Off Manager can reset a refused leave."
msgstr "Лише менеджер з відпустки може скинути відмову у відпустці."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Only a Time Off Manager can reset a started leave."
msgstr "Лише менеджер з відпустки може скинути початок відпустки."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Only a Time Off Manager can reset other people leaves."
msgstr "Лише менеджер відпусток може скинути відпустки інших людей."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"Only a Time Off Officer or Manager can approve/refuse its own requests."
msgstr "Лише керівник або менеджер може схвалити/відхилити власні запити."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Only a manager can modify a canceled leave."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Only a time off Administrator can approve their own requests."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"Only a time off Officer/Responsible or Administrator can approve or refuse "
"allocation requests."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee_base.py:0
msgid "Operation not supported"
msgstr "Операція не підтримується"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__other
msgid "Other"
msgstr "Інше"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
msgid "Out of office"
msgstr "Не на робочому місці"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_dashboard
msgid "Overview"
msgstr "Загальний огляд"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_cl
msgid "Paid Time Off"
msgstr "Оплачувана відпустка"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.hr_holiday_status_dv
msgid "Parental Leaves"
msgstr "Батьківські відпустки"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "Pending Requests"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Period"
msgstr "Період"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__planned
msgid "Planned"
msgstr "Заплановано"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Planned:"
msgstr "Заплановано:"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_present
msgid "Present but on leave"
msgstr "Присутній, але у відпустці"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Print"
msgstr "Друк"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
msgid "Provide a reason to cancel an approved time off"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_form_inherit
msgid "Public"
msgstr "Публічний"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__include_public_holidays_in_duration
msgid "Public Holiday Included"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.actions.act_window,name:hr_holidays.open_view_public_holiday
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_public_time_off_menu_configuration
msgid "Public Holidays"
msgstr "Офіційні вихідні"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__include_public_holidays_in_duration
msgid ""
"Public holidays should be counted in the leave duration when applying for "
"leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value
msgid "Rate"
msgstr "Ставка"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__rating_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__rating_ids
msgid "Ratings"
msgstr "Оцінювання"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__reason
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Reason"
msgstr "Причина"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__notes
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__notes
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__notes
msgid "Reasons"
msgstr "Причини"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/views/calendar/common/calendar_common_popover.xml:0
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Refuse"
msgstr "Відхилити"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__refuse
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_dashboard_new_time_off
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Refused"
msgstr "Відхилено"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Refused Time Off"
msgstr "Відхилена відпустка"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__regular
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_type__regular
msgid "Regular Allocation"
msgstr "Регулярний розподіл "

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Remaining Days"
msgstr "Залишилось днів"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Remaining Hours"
msgstr "Залишилось годин"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Remaining leaves"
msgstr "Залишок відпусток"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_report
msgid "Reporting"
msgstr "Звітність"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Allocation"
msgstr "Запит на розподіл"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_to
msgid "Request End Date"
msgstr "Кінцева дата запиту"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from
msgid "Request Start Date"
msgstr "Початкова дата запиту"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Time off"
msgstr "Запит на відпустку"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__leave_type
msgid "Request Type"
msgstr "Тип запиту"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__duration_display
msgid "Requested (Days/Hours)"
msgstr "Запитані (Дні/Години)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__requires_allocation
msgid "Requires allocation"
msgstr "Вимагає розподілу"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Reset"
msgstr "Скинути"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__resource_calendar_id
msgid "Resource Calendar"
msgstr "Календар ресурсу"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.resource_calendar_global_leaves_action_from_calendar
msgid "Resource Time Off"
msgstr "Відпустка кадру"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Деталі відпустки співробітника"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar
msgid "Resource Working Time"
msgstr "Робочий час ресурсу"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_user_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_user_id
msgid "Responsible User"
msgstr "Відповідальний користувач"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Rules"
msgstr "Правила"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Run until"
msgstr "Запуск до"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Помилка доставки SMS"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sat
msgid "Saturday"
msgstr "Субота"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
msgid "Save"
msgstr "Зберегти"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Search Time Off"
msgstr "Пошук відпустки"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Search Time Off Type"
msgstr "Пошук типу відпустки"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Search allocations"
msgstr "Пошук бронювань"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__second_approver_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__second_approver_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate1
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Second Approval"
msgstr "Друге підтвердження"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day
msgid "Second Day"
msgstr "Другий день"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day_display
msgid "Second Day Display"
msgstr "Відображення другого дня"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month
msgid "Second Month"
msgstr "Другий місяць"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day
msgid "Second Month Day"
msgstr "День другого місяця"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day_display
msgid "Second Month Day Display"
msgstr "Відображення дня другого місяця"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Second approval request for %(allocation_type)s"
msgstr "Другий запит на затвердження для %(allocation_type)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Second approval request for %(leave_type)s"
msgstr "Другий запит на затвердження для %(leave_type)s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Select Time Off"
msgstr "Оберіть відпустку"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__holiday_type
msgid "Select Time Off Type"
msgstr "Обрати тип відпустки"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__validation_type
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__allocation_validation_type
msgid ""
"Select the level of approval needed in case of request by employee\n"
"            #     - No validation needed: The employee's request is automatically approved.\n"
"            #     - Approved by Time Off Officer: The employee's request need to be manually approved\n"
"            #       by the Time Off Officer, Employee's Approver or both."
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Select the request you just created"
msgstr "Оберіть запит, який ви щойно створили"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_report_calendar__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_res_users__leave_manager_id
msgid ""
"Select the user responsible for approving \"Time Off\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""
"Оберіть відповідального користувача за підтвердження \"Відпутски\" цього співробітника.\n"
"Якщо залишити пустим, буде підвтерджено адміністратором або підтверджувачем (визначається у налаштуваннях/користувачах)."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__sep
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__sep
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__sep
msgid "September"
msgstr "Вересень"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__sequence
msgid "Sequence"
msgstr "Послідовність"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "Sequence is generated automatically by start time delta."
msgstr "Послідовність генерується автоматично за дельтою часу початку."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__postpone_max_days
msgid "Set a maximum of accruals an allocation keeps at the end of the year."
msgstr ""
"Встановіть максимальну суму нарахувань, яку зберігає розподіл наприкінці "
"року."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__show_on_dashboard
msgid "Show On Dashboard"
msgstr "Показати на панелі приладів"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__show_transition_mode
msgid "Show Transition Mode"
msgstr "Показати перехідний режим"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Show all records which has next action date is before today"
msgstr "Показати всі записи, які мають дату наступної дії до сьогоднішньої"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_sl
#: model:mail.message.subtype,description:hr_holidays.mt_leave_sick
#: model:mail.message.subtype,name:hr_holidays.mt_leave_sick
msgid "Sick Time Off"
msgstr "Лікарняний"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Some leaves cannot be linked to any allocation. To see those leaves,"
msgstr ""
"Деякі відпустки не можуть бути пов’язані з жодним розподілом. Щоб побачити "
"ці відпустки,"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
msgid ""
"Some of the accrual plans you're trying to delete are linked to an existing "
"allocation. Delete or cancel them first."
msgstr ""
"Деякі з планів накопичення, які ви намагаєтеся видалити, пов’язані з наявним"
" розподілом. Спочатку видаліть або скасуйте їх."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
msgid ""
"Specify if this accrual plan can only be used with this Time Off Type.\n"
"                Leave empty if this accrual plan can be used with any Time Off Type."
msgstr ""
"Укажіть, чи можна використовувати цей план нарахування лише з цим типом відпустки.\n"
"                Залиште порожнім, якщо цей план нарахування можна використовувати з будь-яким типом відпустки."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid ""
"Specify what occurs if a level transition takes place in the middle of a pay period.\n"
"\n"
"                'Immediately' will switch the employee to the new accrual level on the exact date during the ongoing pay period.\n"
"\n"
"                'After this accrual's period' will keep the employee on the same accrual level until the ongoing pay period is complete.\n"
"                After it is complete, the new level will take effect when the next pay period begins."
msgstr ""
"Укажіть, що відбувається, якщо зміна рівня відбувається в середині платіжного періоду.\n"
"\n"
"                «Негайно» переведе працівника на новий рівень нарахування в точну дату протягом поточного періоду оплати.\n"
"\n"
"                «Після цього періоду нарахування» збереже для працівника той самий рівень нарахування, доки не завершиться поточний період виплати.\n"
"                Після його завершення новий рівень набуде чинності з початком наступного платіжного періоду."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Start Accruing"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__start_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_from
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Start Date"
msgstr "Початкова дата"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__start_count
msgid "Start after"
msgstr "Почати після"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__state
msgid "State"
msgstr "Область"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__state
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Status"
msgstr "Статус"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Етап заснований на діях\n"
"Протерміновано: термін виконання вже минув\n"
"Сьогодні: дата дії сьогодні\n"
"Заплановано: майбутні дії."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_striked
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_striked
msgid "Striked"
msgstr "Вибув"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Submit your request"
msgstr "Надішліть ваш запит"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Sum"
msgstr "Всього"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sun
msgid "Sunday"
msgstr "Неділя"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids_count
msgid "Supported Attachment Ids Count"
msgstr "Кількість Id підтримуваних прикріплень"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_support_document
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__support_document
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Supporting Document"
msgstr "Підтримуваний документ"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Supporting Documents"
msgstr "Підримувані документи"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__request_unit
msgid "Take Time Off in"
msgstr "Взяти відпустку на"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__taken
msgid "Taken"
msgstr "Взятий"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Код країни ISO у двох символах.\n"
"Ви можете використовувати це поле для швидкого пошуку."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"The Start Date of the Validity Period must be anterior to the End Date."
msgstr "Дата початку Періоду дії має бути до Кінцевої дати."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_count
msgid ""
"The accrual starts after a defined period from the allocation start date. "
"This field defines the number of days, months or years after which accrual "
"is used."
msgstr ""
"Нарахування починається після визначеного періоду з дати початку розподілу. "
"Це поле визначає кількість днів, місяців або років, після яких "
"використовується нарахування."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid ""
"The allocation requirement of a time off type cannot be changed once leaves "
"of that type have been taken. You should create a new time off type instead."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__color
msgid ""
"The color selected here will be used in every screen with the time off type."
msgstr ""
"Вибраний тут колір буде використовуватися на кожному екрані з типом "
"відпустки."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_check_dates
msgid "The dates you've set up aren't correct. Please check them."
msgstr "Дати, які ви встановили, неправильні. Будь ласка, перевірте їх."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__time_type
msgid ""
"The distinction between working time (ex. Attendance) and absence (ex. "
"Training) will be used in the computation of Accrual's plan rate."
msgstr ""
"Різниця між робочим часом (наприклад, відвідуваність) і відсутністю "
"(наприклад, навчання) використовуватиметься при розрахунку рейту плану "
"нарахування."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_allocation_duration_check
msgid "The duration must be greater than 0."
msgstr "Тривалість має бути більше 0."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_departure_wizard.py:0
msgid "The employee no longer works in the company"
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"The following employees are not supposed to work during that period:\n"
" %s"
msgstr ""
"Для наступних співробітників не передбачено працювати протягом цього періоду:\n"
" %s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid ""
"The leaves planned in the future are exceeding the maximum value of the allocation.\n"
"                It will not be possible to take all of them."
msgstr ""
"Відпустки, заплановані в майбутньому, перевищують максимальне значення розподілу.\n"
"Усе взяти не вийде."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_type_check_negative
msgid ""
"The maximum excess amount should be greater than 0. If you want to set 0, "
"disable the negative cap instead."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__expiring_carryover_days
msgid ""
"The number of carried over days that will expire on "
"carried_over_days_expiration_date"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__added_value
msgid ""
"The number of hours/days that will be incremented in the specified Time Off "
"Type for every period"
msgstr ""
"Кількість годин/днів, яка буде збільшена до вказаного типу відпустки для "
"кожного періоду"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_date_check3
msgid ""
"The request start date must be before or equal to the request end date."
msgstr ""
"Дата початку запиту має передувати або дорівнювати даті завершення запиту."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_mandatory_day_date_from_after_day_to
msgid "The start date must be anterior than the end date."
msgstr "Дата початку має бути перед датою завершення."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_date_check2
msgid "The start date must be before or equal to the end date."
msgstr "Дата початку має передувати даті завершення або дорівнювати їй."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__state
msgid ""
"The status is 'To Approve', when an allocation request is created.\n"
"The status is 'Refused', when an allocation request is refused by manager.\n"
"The status is 'Approved', when an allocation request is approved by manager."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__state
msgid ""
"The status is set to 'To Submit', when a time off request is created.\n"
"The status is 'To Approve', when time off request is confirmed by user.\n"
"The status is 'Refused', when time off request is refused by manager.\n"
"The status is 'Approved', when time off request is approved by manager."
msgstr ""
"Статус встановлено на 'Відправити', коли створюється запит на відпустку.\n"
"Статус встановлено на 'Затвердити', коли запит на відпустку підтверджено користувачем.\n"
"Статус встановлено на 'Відмовлено', коли менеджер відхиляє запит на відпустку.\n"
"Статус встановлено на 'Затверджено', коли запит на відпустку підтверджено менеджером."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "The time off has been automatically approved"
msgstr "Відпустка автоматично затверджена"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "The time off has been cancelled: %s"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__sequence
msgid ""
"The type with the smallest sequence is the default value in time off request"
msgstr ""
"Тип з найменшою послідовністю - це значення за замовчуванням у запиті на "
"відпустку"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"There is no employee set on the time off. Please make sure you're logged in "
"the correct company."
msgstr ""
"На відпустці не вказано співробітника. Будь ласка, переконайтесь, що ви "
"ввійшли у правильну компанію."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "There is no valid allocation to cover that request."
msgstr "Немає дійсного розподілу для покриття цього запиту."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"This allocation have already ran once, any modification won't be effective "
"to the days allocated to the employee. If you need to change the "
"configuration of the allocation, delete and create a new one."
msgstr ""
"Цей розподіл уже запущено один раз, будь-які зміни не будуть застосовані до "
"днів, призначених працівнику. Якщо потрібно змінити конфігурацію розподілу, "
"видаліть і створіть новий."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__first_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off"
msgstr ""
"Ця область автоматично заповнюється користувачем, який перевіряє відпустку"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__second_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off with"
" second level (If time off type need second validation)"
msgstr ""
"Ця область автоматично заповнюється користувачем, який підтверджує відпустку"
" з другим рівнем (Якщо тип відпустки вимагає другого підтвердження)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__approver_id
msgid ""
"This area is automatically filled by the user who validates the allocation"
msgstr ""
"Ця область заповнюється автоматично користувачем, який підтверджує "
"розподілення"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__second_approver_id
msgid ""
"This area is automatically filled by the user who validates the allocation "
"with second level (If time off type need second validation)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__accrual_validity_type
msgid "This field defines the unit of time after which the accrual ends."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_type
msgid "This field defines the unit of time after which the accrual starts."
msgstr "Це поле визначає одиницю часу, після якої починається нарахування."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "This indicates if it is still possible to use this type of leave"
msgstr "Це вказує, чи можна використовувати цей тип відпустки"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "This modification is not allowed in the current state."
msgstr "Ця зміна не дозволена у поточному стані."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "This time off cannot be cancelled."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__leaves_taken
msgid ""
"This value is given by the sum of all time off requests with a negative "
"value."
msgstr ""
"Це значення задається сумою усіх запитів на відпустку з від'ємним значенням."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__max_leaves
msgid ""
"This value is given by the sum of all time off requests with a positive "
"value."
msgstr ""
"Це значення задається сумою всіх запитів на відпустку з позитивним "
"значенням."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__thu
msgid "Thursday"
msgstr "Четвер"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_holiday_allocation_id
#: model:ir.model,name:hr_holidays.model_hr_leave
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_manager_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__request
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_root
#: model:ir.ui.menu,name:hr_holidays.menu_open_department_leave_approve
#: model:mail.message.subtype,name:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
msgid "Time Off"
msgstr "Відпустка"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr "Розподіл на відпустку"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/hr_leave_employee_type_report.py:0
#: model:ir.actions.act_window,name:hr_holidays.action_hr_available_holidays_report
#: model:ir.actions.act_window,name:hr_holidays.action_hr_leave_report
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_report_action
msgid "Time Off Analysis"
msgstr "Аналіз відпустки"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_approval
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
msgid "Time Off Approval"
msgstr "Дозвіл на відпустку"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_tree_inherit_leave
msgid "Time Off Approver"
msgstr "Підтверджувач відпусток"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_report_calendar
msgid "Time Off Calendar"
msgstr "Календар відпусток"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar__associated_leaves_count
msgid "Time Off Count"
msgstr "Підрахунок відпусток"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee.py:0
msgid "Time Off Dashboard"
msgstr "Панель приладів відпустки"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__private_name
msgid "Time Off Description"
msgstr "Опис відпустки"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_notif_subtype_id
msgid "Time Off Notification Subtype"
msgstr "Підтип сповіщення про відпустку"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid ""
"Time Off Officers allocate time off days to employees (e.g. paid time off).<br>\n"
"                Employees request allocations to Time Off Officers (e.g. recuperation days)."
msgstr ""
"Керівник відпустки розподіляє дні співробітникам (напр., оплачувана відпустка).<br>\n"
"                Запит співробітників призначається керівникам відпусток (напр., дні відновлення)."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my_request
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__leave_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__leave_id
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar_leaves__holiday_id
#: model:mail.message.subtype,description:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_employee_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Time Off Request"
msgstr "Запит на відпустку"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Time Off Requests"
msgstr "Запити на відпустку"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_responsible
msgid "Time Off Responsible"
msgstr "Відповідальний за відпустку"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_second_approval
msgid "Time Off Second Approve"
msgstr "Друге підтвердження відпустки"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_summary_employee
#: model:ir.actions.report,name:hr_holidays.action_report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_list
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_pivot
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Time Off Summary"
msgstr "Підсумок відпустки"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_employee_type_report
#: model:ir.model,name:hr_holidays.model_hr_leave_report
msgid "Time Off Summary / Report"
msgstr "Підсумок/Звіт відпустки"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Time Off Taken:"
msgstr "Використана відпустка:"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off Type"
msgstr "Тип відпустки"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_holiday_status
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_status_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Time Off Types"
msgstr "Типи відпустки"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_validation_type
msgid "Time Off Validation"
msgstr "Затвердження відпустки"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off of Your Team Member"
msgstr "Відпустка члена вашої команди"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__leave_to_approve_count
msgid "Time Off to Approve"
msgstr "Відпустка на затвердження"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Time Off."
msgstr "Відпустка."

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.hr_leave_cron_cancel_invalid_ir_actions_server
msgid "Time Off: Cancel invalid leaves"
msgstr "Час вимкнення: скасування недійсних відпусток"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Time off"
msgstr "Відпустка"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leaves_taken
msgid "Time off Already Taken"
msgstr "Відпустка вже використана"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_holidays_by_employee_and_type_report
msgid "Time off Analysis by Employee and Time Off Type"
msgstr "Аналіз відпустки за співробітником та типом відпустки"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_pivot
msgid "Time off Summary"
msgstr "Підсумок відпустки"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__leaves_taken
msgid "Time off Taken"
msgstr "Зайняти відпустка"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time off of people you are manager of"
msgstr "Відпустка людей, якими ви керуєте"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"Time off request must be confirmed (\"To Approve\") in order to approve it."
msgstr ""
"Запит на відпустку потрібно підтвердити (\"Підтвердити\") щоб схвалити його."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Time off request must be confirmed in order to approve it."
msgstr "Запит на відпустку потрібно підтвердити, щоб схвалити його."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Time off request must be confirmed or validated in order to refuse it."
msgstr ""
"Запит на відпустку потрібно підтвердити чи перевірити, щоби відхилити його."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"Time off request state must be \"Refused\" or \"Cancelled\" in order to be "
"reset to \"Confirmed\"."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__tz
msgid "Timezone"
msgstr "Часовий пояс"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Title"
msgstr "Заголовок"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__stop_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "To"
msgstr "До"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__confirm
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "To Approve"
msgstr "Необхідно затвердити"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "To Approve or Approved Allocations"
msgstr "Затвердити або Затверджені розподіли"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_date_to
msgid "To Date"
msgstr "По дату"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "Today"
msgstr "Сьогодні"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Today Activities"
msgstr "Сьогоднішні дії"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocations_count
msgid "Total number of allocations"
msgstr "Загальна кількість розподілу"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_count
msgid "Total number of days allocated."
msgstr "Загальна кількість днів розподілена."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__remaining_leaves
msgid ""
"Total number of paid time off allocated to this employee, change this value "
"to create allocation/time off request. Total based on all the time off types"
" without overriding limit."
msgstr ""
"Загальна кількість оплачуваної відпустки розподілено співробітнику, змініть "
"це значення, щоби створити розподілення/запит на відпустку. Сума базується "
"на всіх типах відпусток без перевизначення ліміту."

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_training
msgid "Training Time Off"
msgstr "Відпустка тренування"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.mail_activity_type_action_config_hr_holidays
msgid ""
"Try to add some records, or make sure that there is no active filter in the "
"search bar."
msgstr ""
"Спробуйте додати кілька записів або переконайтеся, що в рядку пошуку немає "
"активного фільтра."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__tue
msgid "Tuesday"
msgstr "Вівторок"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__bimonthly
msgid "Twice a month"
msgstr "Двічі на місяць"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__biyearly
msgid "Twice a year"
msgstr "Двічі на рік"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
msgid ""
"Two public holidays cannot overlap each other for the same working hours."
msgstr ""
"Два державних свята не можуть накладатися один на одного на однакові робочі "
"години."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Type"
msgstr "Тип"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__type_request_unit
msgid "Type Request Unit"
msgstr "Тип запиту"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Тип дії виключення на записі."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz
msgid "Tz"
msgstr "Tz"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz_mismatch
msgid "Tz Mismatch"
msgstr "Співставлення Tz"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Unlimited"
msgstr "Необмежено"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_unpaid
msgid "Unpaid"
msgstr "Неоплачувана"

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_leave_unpaid
#: model:mail.message.subtype,name:hr_holidays.mt_leave_unpaid
msgid "Unpaid Time Off"
msgstr "Неоплачувана відпустка"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Unread Messages"
msgstr "Непрочитані повідомлення"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Up to"
msgstr "Збільшити"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_users
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__user_id
msgid "User"
msgstr "Користувач"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
msgid "User is idle"
msgstr "Користувач незайнятий"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
msgid "User is online"
msgstr "Користувач онлайн"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
msgid "User is out of office"
msgstr "Користувач не на робочому місці"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/static/src/views/calendar/common/calendar_common_popover.xml:0
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Validate"
msgstr "Підтвердити"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
msgid "Validated"
msgstr "Підтверджено"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__validation_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__validation_type
msgid "Validation Type"
msgstr "Тип валідації"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_departure_wizard.py:0
msgid ""
"Validity End date has been updated because Employee no longer works in the "
"company"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "Validity Period"
msgstr "Період дії"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Start"
msgstr "Почати дію"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Stop"
msgstr "Зупинити дію"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid "Virtual Remaining Time Off"
msgstr "Віртуальний залишок відпустки"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__confirm
msgid "Waiting Approval"
msgstr "Очікує затвердження"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Waiting For Me"
msgstr "Очікує на мене"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate1
msgid "Waiting Second Approval"
msgstr "Очікує другого затвердження"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Waiting for Approval"
msgstr "Очікує затвердження"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__website_message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__website_message_ids
msgid "Website Messages"
msgstr "Повідомлення з веб-сайту"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__website_message_ids
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__website_message_ids
msgid "Website communication history"
msgstr "Історія бесіди на сайті"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__wed
msgid "Wednesday"
msgstr "Середа"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__weekly
msgid "Weekly"
msgstr "Щотижня"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__action_with_unused_accruals
msgid ""
"When the Carry-Over Time is reached, according to Plan's setting, select "
"what you want to happen with the unused time off: None (time will be reset "
"to zero), All accrued time carried over to the next period; or Carryover "
"with a maximum)."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time
msgid ""
"When the field is checked the balance of an allocation using this accrual "
"plan will never exceed the specified amount."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time_yearly
msgid ""
"When the field is checked the total amount accrued each year will be capped "
"at the specified amount"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__other
msgid "Worked Time"
msgstr "Відпрацьований час"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__resource_calendar_id
msgid "Working Hours"
msgstr "Робочі години"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__yearly
msgid "Yearly"
msgstr "Щорічно"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day
msgid "Yearly Day"
msgstr "Річний день"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day_display
msgid "Yearly Day Display"
msgstr "Відображення річного дня"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_month
msgid "Yearly Month"
msgstr "Річний місяць"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__maximum_leave_yearly
msgid "Yearly limit to"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__year
msgid "Years"
msgstr "Роки"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__yes
msgid "Yes"
msgstr "Так"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__requires_allocation
msgid ""
"Yes: Time off requests need to have a valid allocation.\n"
"\n"
"              No Limit: Time Off requests can be taken without any prior allocation."
msgstr ""
"Так: Запити на відпустку повинні мати дійсний розподіл.\n"
"\n"
"              Без обмежень: Запити на відпустку можна взяти без попереднього розподілу."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "You are not allowed to request time off on a Mandatory Day"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__accrual_validity_count
msgid ""
"You can define a period of time where the days carried over will be "
"available"
msgstr ""

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_start_count_check
msgid "You can not start an accrual in the past."
msgstr "Ви не можете почати розподіл у минулому."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid ""
"You can select the period you need to take off, from start date to end date"
msgstr ""
"Ви можете вибрати період, який потрібно зняти, від дати початку до дати "
"завершення"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "You cannot delete a time off which is in %s state"
msgstr "Ви не можете видалити відпустку, яка є на етапі %s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "You cannot delete a time off which is in the past"
msgstr "Ви не можете видалити відпустку у минулому"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"You cannot delete an allocation request which has some validated leaves."
msgstr ""
"Ви не можете видалити запит на розпоідлення, який вже має деякі затверджені "
"відпустки."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "You cannot delete an allocation request which is in %s state."
msgstr ""
"Ви не можете видалити запит на бронювання, який знаходиться у статусі %s."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You cannot first approve a time off for %s, because you are not his time off"
" manager"
msgstr ""
"Ви не можете спершу затвердити відпустку для %s, тому що ви не його менеджер"
" відпусток"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan_level.py:0
msgid ""
"You cannot have a cap on accrued time without setting a maximum amount."
msgstr ""
"Ви не можете встановити обмеження на накопичений час без встановлення "
"максимальної суми."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_valid_yearly_cap_value
msgid ""
"You cannot have a cap on yearly accrued time without setting a maximum "
"amount."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid ""
"You cannot modify the 'Public Holiday Included' setting since one or more "
"leaves for that                         time off type are overlapping with "
"public holidays, meaning that the balance of those employees would be "
"affected by this change."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"You cannot reduce the duration below the duration of leaves already taken by"
" the employee."
msgstr ""
"Ви не можете скоротити тривалість нижче тривалості відпустки, яку працівник "
"уже взяв."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"You cannot refuse this allocation request since the employee has already "
"taken leaves for it. Please refuse or delete those leaves first."
msgstr ""
"Ви не можете відмовити у цьому запиті на розподіл, оскільки працівник уже "
"взяв для цього відпустку. Будь ласка, спочатку відмовтеся або видаліть ці "
"відпустки."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You don't have the rights to apply second approval on a time off request"
msgstr "У вас немає прав застосувати друге затвердження запиту на відпустку"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "You must be %s's Manager to approve this leave"
msgstr "Ви повинні бути менеджером %s для затвердження цієї відпустки"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"You must be either %s's Time Off Approver or Time off Administrator to "
"validate this allocation request."
msgstr ""

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You must be either %s's manager or Time off Manager to approve this leave"
msgstr ""
"Ви повинні бути або менеджером %s або менеджером відпусток для затвердження "
"цієї відпустки"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You must either be a Time off Officer or Time off Manager to approve this "
"leave"
msgstr ""
"Ви повинні бути керівником чи менеджером відусток, щоби затвердити цю "
"відпустку"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_added_value_greater_than_zero
msgid "You must give a rate greater than 0 in accrual plan levels."
msgstr "Ви повинні вказати ставку більше 0 на рівнях плану нарахування."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You must have manager rights to modify/validate a time off that already "
"begun"
msgstr ""
"Вам потрібні права менеджера для зміни/підтвердження відпустки, яка вже "
"почалася"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You've already booked time off which overlaps with this period:\n"
"%s\n"
"Attempting to double-book your time off won't magically make your vacation 2x better!\n"
msgstr ""
"Ви вже забронювали вихідний, який збігається з цим періодом:\n"
"%s\n"
"Спроба подвійного бронювання відпустки не зробить вашу відпустку вдвічі кращою!\n"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Your %(leave_type)s planned on %(date)s has been accepted"
msgstr "Ваш %(leave_type)s запланований на %(date)s прийнято"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Your %(leave_type)s planned on %(date)s has been refused"
msgstr "Ваш %(leave_type)s запланований на %(date)s відхилено"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Your Time Off"
msgstr "Ваша відпустка"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_holidays_cancel_leave.py:0
msgid "Your time off has been cancelled."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "after"
msgstr "після"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "after allocation start date"
msgstr "після дня початку розподілу"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "all"
msgstr "всі"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "and"
msgstr "і"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "and on the"
msgstr "і на"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "available"
msgstr "в наявності"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_available_holidays_report_tree
msgid "by Employee"
msgstr "за співробітником"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_summary_all
msgid "by Type"
msgstr "за типом"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "can be used before the allocation expires."
msgstr "можна використати до закінчення терміну розподілу."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "click here"
msgstr "натисніть тут"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "day of the month"
msgstr "день місяця"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "day(s)"
msgstr "день(дні)"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "days"
msgstr "дні"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "days of the months"
msgstr "дні місяців"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "days)"
msgstr "дні)"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "e.g. Extra recuperation, Company unavailability, ..."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "e.g. Time Off type (From validity start to validity end / no limit)"
msgstr ""
"напр., Тип відпустки (Від початку підтвердження до кінця / без обмежень)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "from %(date_from)s to %(date_to)s - %(state)s"
msgstr "з %(date_from)s до %(date_to)s - %(state)s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "hour(s)"
msgstr ""

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "hours"
msgstr "годин"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "in"
msgstr "в"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "initially"
msgstr "спочатку"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan_level.py:0
msgid "last day"
msgstr "останній день"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "new request"
msgstr "новий запит"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "no"
msgstr "ні"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "of"
msgstr "від"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "of the"
msgstr "для"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "of the month"
msgstr "місяця"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on"
msgstr "на"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on the"
msgstr "на"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "refused"
msgstr "відхилено"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "sequence"
msgstr "послідовність"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "taken"
msgstr "використано"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "the accruated amount is insufficient for that duration."
msgstr "нарахована сума недостатня для цього періоду."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "to"
msgstr "до"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "to refuse"
msgstr "відхилити"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "up to"
msgstr "збільшити до"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "valid until"
msgstr "дійсний до"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "validate"
msgstr "підтвердити"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "validated"
msgstr "підтверджено"
