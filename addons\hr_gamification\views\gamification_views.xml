<?xml version="1.0" encoding="UTF-8"?>
<odoo>

        <record id="hr_badge_form_view" model="ir.ui.view">
            <field name="name">gamification.badge.form.inherit</field>
            <field name="model">gamification.badge</field>
            <field name="inherit_id" ref="gamification.badge_form_view"/>
            <field name="arch" type="xml">
                <div name="button_box" position="inside">
                    <button class="oe_stat_button" type="object" name="get_granted_employees" invisible="granted_count == 0" icon="fa-user">
                        <field name="granted_employees_count" string="Granted" widget="statinfo"/>
                    </button>
                </div>
            </field>
        </record>

        <record id="goals_menu_groupby_action2" model="ir.actions.act_window">
            <field name="res_model">gamification.goal</field>
            <field name="name">Goals History</field>
            <field name="view_mode">list,kanban</field>
            <field name="context">{'search_default_group_by_user': True, 'search_default_group_by_definition': True}</field>
            <field name="domain">[('challenge_id.challenge_category', '=', 'hr')]</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new goal
                </p><p>
                    A goal is defined by a user and a goal type.
                    Goals can be created automatically by using challenges.
                </p>
            </field>
        </record>


        <record id="challenge_list_action2" model="ir.actions.act_window">
            <field name="name">Challenges</field>
            <field name="res_model">gamification.challenge</field>
            <field name="view_mode">kanban,list,form</field>
            <field name="domain">[('challenge_category', '=', 'hr')]</field>
            <field name="context">{'search_default_inprogress':True, 'default_inprogress':True}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new challenge
                </p><p>
                    Assign a list of goals to chosen users to evaluate them.
                    The challenge can use a period (weekly, monthly...) for automatic creation of goals.
                    The goals are created for the specified users or member of the group.
                </p>
            </field>
        </record>
        <record id="challenge_list_action2_view1" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">kanban</field>
            <field name="act_window_id" ref="challenge_list_action2"/>
            <field name="view_id" ref="gamification.view_challenge_kanban"/>
        </record>
        <record id="challenge_list_action2_view2" model="ir.actions.act_window.view">
            <field name="sequence" eval="10"/>
            <field name="view_mode">form</field>
            <field name="act_window_id" ref="challenge_list_action2"/>
            <field name="view_id" ref="gamification.challenge_form_view"/>
        </record>

        <menuitem id="menu_hr_gamification" parent="hr.menu_human_resources_configuration" name="Challenges" sequence="100"/>

        <menuitem id="gamification_badge_menu_hr" parent="menu_hr_gamification" action="gamification.badge_list_action" />
        <menuitem id="gamification_challenge_menu_hr" parent="menu_hr_gamification" action="challenge_list_action2" groups="hr.group_hr_user"/>
        <menuitem id="gamification_goal_menu_hr" parent="menu_hr_gamification" action="goals_menu_groupby_action2" groups="hr.group_hr_user"/>

</odoo>
